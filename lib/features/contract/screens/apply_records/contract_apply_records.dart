import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/utils/utils.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/pagination/common_refresher.dart';
import '../../../account/domain/models/account_summary/contract_summary_response.dart';
import '../../domain/models/contract_apply_records/contract_apply_records.dart';
import '../../logic/contract/contract_cubit.dart';
import 'widgets/apply_contracts_shimmer.dart';

class ContractApplyRecordsScreen extends StatefulWidget {
  const ContractApplyRecordsScreen({super.key});

  @override
  State<ContractApplyRecordsScreen> createState() => _ContractApplyRecordsScreenState();
}

class _ContractApplyRecordsScreenState extends State<ContractApplyRecordsScreen> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
  }

  void _init() => context.read<ContractCubit>().getContractApplyRecords();

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('apply_records'.tr()), // 申请记录
        surfaceTintColor: Colors.transparent,
      ),
      body: BlocListener<ContractCubit, ContractState>(
        listener: (context, state) {
          if (state.contractApplyRecordsFetchStatus == DataStatus.success) {
            if (state.contractApplyRecords?.records?.length == state.contractApplyRecords?.total) {
              _refreshController.loadNoData();
            } else {
              _refreshController.loadComplete();
            }
            _refreshController.refreshCompleted();
          } else if (state.contractApplyRecordsFetchStatus == DataStatus.failed) {
            _refreshController.refreshFailed();
            _refreshController.loadFailed();
          }
        },
        child: BlocBuilder<ContractCubit, ContractState>(
          builder: (context, state) {
            if (state.contractApplyRecordsFetchStatus == DataStatus.loading &&
                (state.contractApplyRecords?.records?.isEmpty ?? true)) {
              return const ApplyContractsShimmer();
            }
            if (state.contractApplyRecordsFetchStatus == DataStatus.failed &&
                (state.contractApplyRecords?.records?.isEmpty ?? true)) {
              return Center(child: Text(state.error ?? '加载失败'));
            }
            return CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              enablePullUp: state.contractApplyRecords?.hasNext ?? false,
              onRefresh: () async {
                context.read<ContractCubit>().getContractApplyRecords(isLoadMore: false);
              },
              onLoading: () async {
                context.read<ContractCubit>().getContractApplyRecords(isLoadMore: true);
              },
              child: state.contractApplyRecords?.records?.isNotEmpty ?? false
                  ? AnimationLimiter(
                      child: ListView.separated(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: EdgeInsets.fromLTRB(12, 12, 12, 24),
                        itemBuilder: (context, index) {
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 600),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: ContractApplyRecordItem(
                                  record: state.contractApplyRecords?.records?[index] ?? ContractApplyRecord(),
                                ),
                              ),
                            ),
                          );
                        },
                        itemCount: state.contractApplyRecords?.records?.length ?? 0,
                        separatorBuilder: (context, index) => 10.verticalSpace,
                      ),
                    )
                  : Center(child: TableEmptyWidget(height: 60.gh, width: 60.gw)),
            );
          },
        ),
      ),
    );
  }
}

class ContractApplyRecordItem extends StatelessWidget {
  final ContractApplyRecord record;
  const ContractApplyRecordItem({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 300),
      child: ScaleAnimation(
        scale: 0.95,
        child: ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    getContractLabel(
                      ContractSummaryData(
                        id: record.id,
                        type: record.type,
                        periodType: record.periodType,
                        marketType: record.marketType,
                        multiple: record.multiple,
                      ),
                    ),
                    style: context.textTheme.primary.fs12.w600,
                  ),
                  Text(
                    ContractAuditStatus.values[record.auditStatus ?? 0].text.tr(),
                    style: context.textTheme.primary.copyWith(
                      color: ContractAuditStatus.values[record.auditStatus ?? 0].color(context),
                    ),
                  ),
                ],
              ),
              5.verticalSpace,
              Divider(color: context.theme.dividerColor),
              5.verticalSpace,
              _DataField(
                label: 'contractMargin'.tr(),
                value: (record.totalCash ?? 0).formatWithCommas(),
              ),
              _DataField(
                label: 'totalMargin'.tr(),
                value: (record.totalPower ?? 0).formatWithCommas(),
              ),
              _DataField(
                label: 'applyTime'.tr(),
                value: ConvertHelper.formatDateType1(
                  record.createTime?.toString() ?? '',
                  separator: '-',
                  showSeconds: true,
                ),
                valueStyle: context.textTheme.primary.w600,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DataField extends StatelessWidget {
  final String label;
  final String value;
  final TextStyle? valueStyle;
  const _DataField({
    required this.label,
    required this.value,
    this.valueStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textTheme.regular.fs13,
        ),
        Text(
          value,
          style: valueStyle ?? context.textTheme.primary.w600.ffAkz,
        ),
      ],
    );
  }
}
