part of 'main_cubit.dart';

class MainState extends Equatable {
  final BottomNavType currentTabType;
  final bool showChatFloatWidget;
  const MainState({
    required this.currentTabType,
    this.showChatFloatWidget = true,
  });

  @override
  List<Object?> get props => [currentTabType, showChatFloatWidget];

  MainState copyWith({
    BottomNavType? currentTabType,
    bool? showChatFloatWidget,
  }) {
    return MainState(
      currentTabType: currentTabType ?? this.currentTabType,
      showChatFloatWidget: showChatFloatWidget ?? this.showChatFloatWidget,
    );
  }
}
