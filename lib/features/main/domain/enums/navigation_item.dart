import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_page_v2.dart';
import 'package:gp_stock_app/features/activity/screens/activity_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/home_screen_enter.dart';
import 'package:gp_stock_app/features/market/market_section_screen.dart';
import 'package:gp_stock_app/features/profile/screens/profile_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';

/// 底部导航栏类型
enum BottomNavType { home, account, trade, activity, profile }

/// 底部导航栏页面配置
class BottomNavConfig {
  final BottomNavType type;
  final String title; // 本地化 key
  final String icon;
  final Widget page;
  final bool isShowAppBar;
  final bool isVisible;

  const BottomNavConfig({
    required this.type,
    required this.title,
    required this.icon,
    required this.page,
    this.isShowAppBar = true,
    this.isVisible = true,
  });

  static List<BottomNavConfig> allConfigs(context) => [
        BottomNavConfig(
          type: BottomNavType.home,
          title: 'home',
          icon: Assets.homeIcon,
          page: const HomeScreenEnter(),
          isShowAppBar: AppConfig.instance.flavor == Flavor.yhxt ? false : true,
        ),
        BottomNavConfig(
          type: BottomNavType.account,
          title: AppConfig.instance.flavor == Flavor.yhxt ? 'account' : 'contract',
          icon: Assets.accountIcon,
          page: const AccountScreenV2(),
        ),
        BottomNavConfig(
          type: BottomNavType.trade,
          title: 'quote',
          icon: Assets.tradeIcon,
          page: const MarketSectionScreen(),
        ),
        BottomNavConfig(
          type: BottomNavType.activity,
          title: 'activity',
          icon: Assets.activityIcon,
          page: const ActivityScreen(),
        ),
        BottomNavConfig(
          type: BottomNavType.profile,
          title: 'profile',
          icon: Assets.profileIcon,
          page: const ProfileScreen(),
          isShowAppBar: false,
        ),
      ];
}
