import 'dart:async';

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/endpoint/urls.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/debouncer.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/main/widgets/draggable_float_widget.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:gp_stock_app/shared/widgets/app_bar/app_bar.dart';
import 'package:gp_stock_app/shared/widgets/bottom_navigation_bar/common_bottom_navigation_bar.dart';
import 'package:gp_stock_app/shared/widgets/snackbar/snackbar_helper.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with SingleTickerProviderStateMixin, RouteAware {
  late final PageController _pageController;
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;

  final _doubleTapHandler = DoubleTapHandler();
  OverlayEntry? _overlayEntry;
  bool _showChatFloatWidget = true;
  bool _isFirstBuild = true;

  // Subscriptions for socket events
  final List<StreamSubscription> _socketSubscriptions = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupAnimations();
    _initializeServices();
    _setupInitialState();
    _setupSocketListeners();
  }

  void _setupSocketListeners() {
    final webSocketService = getIt<WebSocketService>();

    // Listen for warning events
    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.warning).listen(_handleWarningMessage));

    // Listen for close events
    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.close).listen(_handleWarningMessage));

    // Listen for system events
    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.system).listen(_handleSystemMessage));

    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.auth).listen(_handleAuth));
  }

  void _handleWarningMessage(WebSocketMessage message) {
    // Extract content from message data
    final data = message.toJson();
    final content = data?['data']?['content'] as String? ?? '_warning'.tr();

    // Show warning notification
    GPEasyLoading.showToast(content);
  }

  void _handleSystemMessage(WebSocketMessage message) {
    // Extract content from message data
    final data = message.toJson();
    final content = data?['data']?['content'] as String? ?? 'System notification';

    // Show system notification
    GPEasyLoading.showToast(content);
  }

  void _initializeControllers() {
    _pageController = PageController(initialPage: 0, keepPage: true);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _setupAnimations() {
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  void _initializeServices() {
    context.read<MainCubit>().selectedNavigationItem(BottomNavType.home);
    context.read<MainCubit>().sendLocale(getIt<NavigatorService>().navigatorKey.currentContext!.locale);

    final webSocketService = getIt<WebSocketService>();
    webSocketService
      ..connect("${HostUtil().currentHost ?? Urls.marketWs}/ws".replaceAll("https", "wss").replaceAll("http", "wss"))
      ..setLogging(false);
  }

  void _setupInitialState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showFloatingChat();
      if (_isFirstBuild) {
        _animationController.forward();
        _isFirstBuild = false;
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    setupRouteObserver();
  }

  void setupRouteObserver() {
    final route = ModalRoute.of(context);
    if (route is! PageRoute) return;

    final observers = Navigator.of(context).widget.observers;
    final routeObserver = observers.whereType<RouteObserver<ModalRoute<void>>>().firstOrNull;
    routeObserver?.subscribe(this, route);
  }

  @override
  void didPopNext() {
    if (_showChatFloatWidget && _overlayEntry == null) {
      _showFloatingChat();
    }
  }

  void _hideFloatingChat() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _showFloatingChat() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => DraggableFloatWidget(
        config: DraggableFloatWidgetConfig(
          marginTop: MediaQuery.of(context).padding.top,
          marginBottom: 100.gh,
          initialPosition: FloatingPosition.bottomRight,
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward();
  }

  void handlePopScope(bool didPop) {
    if (didPop) return;

    final shouldExit = _doubleTapHandler.handle();
    if (shouldExit) {
      SystemNavigator.pop();
    } else {
      showAppSnackBar(
        'pressAgainToExit'.tr(),
        context,
        icon: Icons.exit_to_app,
      );
    }
  }

  void _handleAuth(WebSocketMessage message) {
    final data = message.toJson();
    if (data?['code'] == 401) {
      Helper.logoutUser(needNav: false);
      NetworkHelper.handleMessage(
        'sessionExpired'.tr(),
        type: HandleTypes.customDialog,
        snackBarType: SnackBarType.error,
        onTap: () {
          getIt<NavigatorService>().pushNamedAndRemoveUntil(AppRouter.routeLogin);
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        MultiBlocListener(
          listeners: [
            BlocListener<MainCubit, MainState>(
              listener: (context, state) {
                _pageController.jumpToPage(BottomNavType.values.indexOf(state.currentTabType));
                if (state.currentTabType == BottomNavType.trade) {
                  Future.delayed(const Duration(milliseconds: 500), () {
                    getIt<IndexTradeCubit>().subscribeToTimeline();
                  });
                }
              },
            ),
            BlocListener<MainCubit, MainState>(
              listenWhen: (previous, current) => (previous.currentTabType == BottomNavType.trade &&
                  current.currentTabType != BottomNavType.trade),
              listener: (context, state) {
                getIt<IndexTradeCubit>().unsubscribeFromTimeline();
              },
            ),
          ],
          child: BlocBuilder<MainCubit, MainState>(
            builder: (context, state) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (_showChatFloatWidget != state.showChatFloatWidget) {
                  _showChatFloatWidget = state.showChatFloatWidget;
                  _showChatFloatWidget ? _showFloatingChat() : _hideFloatingChat();
                }
              });

              final bottomNavConfigs = BottomNavConfig.allConfigs(context);
              return PopScope(
                canPop: false,
                onPopInvokedWithResult: (didPop, _) => handlePopScope(didPop),
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.1),
                      end: Offset.zero,
                    ).animate(_animationController),
                    child: Scaffold(
                      appBar: _getAppBar(state:state, list: bottomNavConfigs),
                      body: Column(
                        children: [
                          Expanded(
                            child: PageView(
                              controller: _pageController,
                              physics: const NeverScrollableScrollPhysics(),
                              children: bottomNavConfigs.map((e) => e.page).toList(),
                            ),
                          ),
                          CommonBottomNavigationBar(
                              data: bottomNavConfigs,
                              onTabSwitch: (type) {
                                void action() => context.read<MainCubit>().selectedNavigationItem(type);
                                if (type == BottomNavType.account) {
                                  AuthUtils.verifyAuth(action);
                                } else {
                                  action();
                                }
                              },
                              currentTabType: state.currentTabType)
                        ],
                      ),
                      extendBody: true,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  PreferredSizeWidget? _getAppBar({required MainState state, required List<BottomNavConfig> list}) {
    final item = list.firstWhereOrNull((e) => e.type == state.currentTabType);
    if (item == null || !item.isShowAppBar) return null;

    return const PreferredSize(
      preferredSize: Size.fromHeight(kToolbarHeight),
      child: MainTitle(),
    );
  }

  @override
  void dispose() {
    _hideFloatingChat();
    _pageController.dispose();
    _doubleTapHandler.dispose();
    _animationController.dispose();

    // Cancel all socket subscriptions
    for (var subscription in _socketSubscriptions) {
      subscription.cancel();
    }
    super.dispose();
  }
}
