import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_cubit.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_state.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../core/api/network/endpoint/urls.dart';
import '../../../shared/widgets/error/error_retry_widget.dart';
import '../../../shared/widgets/flip_text.dart';
import '../widgets/invite_shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import '../../../shared/widgets/buttons/common_button.dart';
import '../../../shared/widgets/shadow_box.dart';

class InviteScreen extends StatefulWidget {
  final AppSkinStyle? skinStyle;

  const InviteScreen({super.key, this.skinStyle});

  @override
  State<InviteScreen> createState() => _InviteScreenState();
}

class _InviteScreenState extends State<InviteScreen> {
  @override
  void initState() {
    super.initState();
    context.read<InviteCubit>().getInviteDetails();
  }

  @override
  Widget build(BuildContext context) {
    final currentSkinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;

    return Scaffold(
      appBar: _buildAppBar(context, currentSkinStyle),
      body: BlocBuilder<InviteCubit, InviteState>(
        builder: (context, state) {
          if (state.status.isLoading) {
            return const InviteScreenShimmer();
          }

          if (state.status.isFailed) {
            return ErrorRetryWidget(
              errorMessage: state.error,
              onRetry: () => context.read<InviteCubit>().getInviteDetails(),
            );
          }

          return _buildBody(context, state, currentSkinStyle);
        },
      ),
    );
  }

  /// Build app bar based on skin style
  PreferredSizeWidget _buildAppBar(BuildContext context, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.colorTheme.textSecondary,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.secondary.fs18.w600),
          backgroundColor: context.colorTheme.inviteBackgroundStart,
          elevation: 0,
          foregroundColor: Colors.white,
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.primary.fs18.w600.copyWith(color: Colors.white)),
          backgroundColor: context.colorTheme.inviteBackgroundStart,
          elevation: 0,
          foregroundColor: Colors.white,
        );
    }
  }

  /// Build body based on skin style
  Widget _buildBody(BuildContext context, InviteState state, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return _buildGPBody(context, state);
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return _buildTemplateBody(context, state);
    }
  }

  /// Build GP style body
  Widget _buildGPBody(BuildContext context, InviteState state) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.colorTheme.inviteBackgroundStart,
            context.colorTheme.inviteBackgroundEnd,
          ],
        ),
      ),
      child: Column(
        children: [
          _buildHeader(
            titleColor: Colors.amber[50]!,
            subtitleColor: Colors.amber[50]!,
            skinStyle: AppSkinStyle.kGP,
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: context.theme.dividerColor.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.all(
                          Radius.circular(12.gr),
                        ),
                      ),
                      margin: EdgeInsets.only(top: 10.gh),
                      padding: EdgeInsets.all(16.gr),
                      child: Column(
                        children: [
                          _buildReferralOptions(state, skinStyle: AppSkinStyle.kGP),
                          SizedBox(height: 20.gh),
                          _buildGPGenerateButton(),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.gh),
                    _buildGPCommissionInfo(state),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build template style body
  Widget _buildTemplateBody(BuildContext context, InviteState state) {
    final skinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.colorTheme.inviteBackgroundStart,
            context.colorTheme.inviteBackgroundEnd,
          ],
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gh),
            child: _buildHeader(
              titleColor: Colors.white,
              subtitleColor: Colors.white.withValues(alpha: 0.9),
              skinStyle: skinStyle,
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ShadowBox(
                      padding: EdgeInsets.all(16.gr),
                      borderRadius: BorderRadius.circular(12.gr),
                      child: Column(
                        children: [
                          _buildReferralOptions(state, skinStyle: skinStyle),
                          SizedBox(height: 20.gh),
                          _buildTemplateGenerateButton(),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.gh),
                    _buildTemplateCommissionInfo(state),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader({
    required Color titleColor,
    required Color subtitleColor,
    required AppSkinStyle skinStyle,
  }) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'inviteAndEarnDescription'.tr(),
              style: context.textTheme.primary.fs24.w700.copyWith(color: titleColor),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.gh),
            Text(
              'performCashTradeToEarn'.tr(),
              style: context.textTheme.regular.fs16.copyWith(color: subtitleColor),
              textAlign: TextAlign.center,
            ),
            _buildImageWithButton(skinStyle),
          ],
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'inviteAndEarnDescription'.tr(),
              style: context.textTheme.primary.w700.copyWith(fontSize: 36.gsp, color: titleColor),
              textAlign: TextAlign.center,
            ),
            Text(
              'performCashTradeToEarn'.tr(),
              style: context.textTheme.secondary.fs16.w700.copyWith(color: subtitleColor),
              textAlign: TextAlign.center,
            ),
            _buildImageWithButton(skinStyle),
          ],
        );
    }
  }

  Widget _buildImageWithButton(AppSkinStyle skinStyle) {
    return SizedBox(
      height: 180.gh, // Increased height to accommodate both image and button
      width: double.infinity,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
            child: Image.asset(
              height: 140.gh,
              width: 1.gsw,
              Assets.grabCoinsIcon,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            bottom: 0.gh,
            child: _buildGoButtonContent(skinStyle),
          ),
        ],
      ),
    );
  }

  Widget _buildGoButtonContent(AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return Container(
          width: 330.gw,
          padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gh),
          decoration: BoxDecoration(
            color: context.theme.dividerColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(20.gr),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'levelingUpToEarnMore'.tr(),
                style: context.textTheme.secondary.fs12,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 6.gh),
                decoration: BoxDecoration(
                  color: const Color(0xFFE06821),
                  borderRadius: BorderRadius.circular(20.gr),
                ),
                child: Text('GO', style: context.textTheme.secondary.fs12.w700),
              ),
            ],
          ),
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 6.gh),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(20.gr),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'levelingUpToEarnMore'.tr(),
                style: context.textTheme.primary.fs12,
              ),
              6.horizontalSpace,
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 6.gh),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.gr),
                  color: context.colorTheme.textSecondary,
                ),
                child: Text(
                  'GO',
                  style: context.textTheme.primary.fs12.w700,
                ),
              ),
            ],
          ),
        );
    }
  }

  Widget _buildReferralOptions(InviteState state, {required AppSkinStyle skinStyle}) {
    final inviteCode = state.inviteDetail?.inviteCode ?? '';
    final inviteLink = state.customInviteLink ?? '${Urls.inviteLink}$inviteCode';

    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => _showQrCodeDialog(context, inviteLink),
            child: Container(
              height: 90.gh,
              padding: EdgeInsets.symmetric(
                vertical: 16.gh,
                horizontal: 16.gw,
              ),
              decoration: _getReferralCardDecoration(skinStyle),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.qr_code_2,
                    size: skinStyle == AppSkinStyle.kGP ? 22.gr : 28.gr,
                    color: skinStyle == AppSkinStyle.kGP
                        ? context.colorTheme.stockRed
                        : skinStyle == AppSkinStyle.kTemplateD
                            ? context.colorTheme.textSecondary
                            : context.colorTheme.textPrimary,
                  ),
                  SizedBox(height: skinStyle == AppSkinStyle.kGP ? 10.gh : 8.gh),
                  Text(
                    'qrCode'.tr(),
                    style: skinStyle == AppSkinStyle.kGP
                        ? context.textTheme.stockRed
                        : skinStyle == AppSkinStyle.kTemplateD
                            ? context.textTheme.secondary
                            : context.textTheme.primary.fs12.w500,
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: skinStyle == AppSkinStyle.kGP ? 16.gw : 12.gw),
        Expanded(
          child: GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: inviteCode));
              Helper.showFlutterToast('inviteCodeCopiedToClipboard'.tr());
            },
            child: Container(
              height: 90.gh,
              padding: EdgeInsets.symmetric(
                vertical: 16.gh,
                horizontal: 16.gw,
              ),
              decoration: _getReferralCardDecoration(skinStyle),
              child: _buildInviteCodeContent(inviteCode, skinStyle),
            ),
          ),
        ),
      ],
    );
  }

  BoxDecoration _getReferralCardDecoration(AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return BoxDecoration(
          color: Colors.pink[50]!,
          borderRadius: BorderRadius.circular(10.gr),
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.colorTheme.inviteBackgroundStart.withValues(alpha: 0.5),
              context.colorTheme.inviteBackgroundEnd,
            ],
          ),
          borderRadius: BorderRadius.circular(12.gr),
        );
      case AppSkinStyle.kTemplateD:
        return BoxDecoration(
          color: context.theme.primaryColor,
          borderRadius: BorderRadius.circular(12.gr),
        );
    }
  }

  Widget _buildInviteCodeContent(String inviteCode, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        inviteCode,
                        style: context.textTheme.stockRed,
                        overflow: TextOverflow.ellipsis,
                      ),
                      8.horizontalSpace,
                      Icon(
                        Icons.copy,
                        size: 20.gr,
                        color: context.colorTheme.stockRed,
                      ),
                    ],
                  ),
                  10.verticalSpace,
                  Text(
                    'inviteCode'.tr(),
                    style: context.textTheme.stockRed,
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.gw),
          ],
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    inviteCode,
                    style: context.textTheme.primary.fs12.w600,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                4.horizontalSpace,
                Icon(
                  Icons.copy,
                  size: 16.gr,
                  color: context.colorTheme.textPrimary,
                ),
              ],
            ),
            8.verticalSpace,
            Text(
              'inviteCode'.tr(),
              style: context.textTheme.primary.fs12.w500,
            ),
          ],
        );
      case AppSkinStyle.kTemplateD:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    inviteCode,
                    style: context.textTheme.secondary.fs12.w600,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                4.horizontalSpace,
                Icon(
                  Icons.copy,
                  size: 16.gr,
                  color: context.colorTheme.textSecondary,
                ),
              ],
            ),
            8.verticalSpace,
            Text(
              'inviteCode'.tr(),
              style: context.textTheme.secondary.fs12.w500,
            ),
          ],
        );
    }
  }

  Widget _buildGPGenerateButton() {
    return BlocBuilder<InviteCubit, InviteState>(
      builder: (context, state) {
        final inviteCode = state.inviteDetail?.inviteCode ?? '';
        // Use custom invite link if available, otherwise use default link
        final inviteLink = state.customInviteLink ?? '${Urls.inviteLink}$inviteCode';

        return ElevatedButton.icon(
          onPressed: () {
            Clipboard.setData(ClipboardData(text: inviteLink));
            Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
          },
          style: ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, 38.gh),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.gr),
            ),
          ),
          icon: Icon(Icons.link, color: context.theme.hintColor),
          label: Text(
            'generateInviteLink'.tr(),
            style: context.textTheme.title.fs16.w600,
          ),
        );
      },
    );
  }

  Widget _buildGPCommissionInfo(InviteState state) {
    return Container(
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.dividerColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(10.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'commissionRate'.tr(),
                      style: context.textTheme.regular,
                    ),
                    SizedBox(height: 10.gh),
                    Text(
                      '${state.inviteDetail?.commissionRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.stockRed.fs24.w700,
                    ),
                  ],
                ),
              ),
              Container(
                height: 50.gh,
                width: 1,
                color: context.theme.dividerColor,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'interestRateRatio'.tr(),
                      style: context.textTheme.regular,
                    ),
                    SizedBox(height: 10.gh),
                    Text(
                      '${state.inviteDetail?.interestRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.stockRed.fs24.w700,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 20.gh),
          Divider(
            height: 1,
            color: context.theme.dividerColor,
          ),
          SizedBox(height: 20.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'myCommission'.tr(),
                style: context.textTheme.regular,
              ),
              FlipText(
                state.inviteDetail?.totalCommission ?? 0.00,
                style: context.textTheme.regular.w700,
                isCurrency: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateGenerateButton() {
    return BlocBuilder<InviteCubit, InviteState>(
      builder: (context, state) {
        final inviteCode = state.inviteDetail?.inviteCode ?? '';
        // Use custom invite link if available, otherwise use default link
        final inviteLink = state.customInviteLink ?? '${Urls.inviteLink}$inviteCode';

        return CommonButton(
          title: 'generateInviteLink'.tr(),
          style: CommonButtonStyle.primary,
          height: 44.gh,
          onPressed: () {
            Clipboard.setData(ClipboardData(text: inviteLink));
            Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
          },
        );
      },
    );
  }

  Widget _buildTemplateCommissionInfo(InviteState state) {
    return ShadowBox(
      padding: EdgeInsets.fromLTRB(16.gw, 0, 16.gw, 16.gh),
      borderRadius: BorderRadius.circular(12.gr),
      child: Column(
        children: [
          SizedBox(
            width: 140.gw,
            height: 30.gh,
            child: Stack(
              alignment: Alignment.center,
              children: [
                IconHelper.loadAsset(
                  Assets.inviteBtnBg,
                  width: 150.gw,
                  height: 30.gh,
                  fit: BoxFit.cover,
                  color: context.theme.primaryColor,
                ),
                Text(
                  'myRebateData'.tr(),
                  style: context.textTheme.secondary.w600,
                ),
              ],
            ),
          ),
          12.verticalSpace,
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'commissionRate'.tr(),
                      style: context.textTheme.highlight,
                    ),
                    SizedBox(height: 8.gh),
                    Text(
                      '${state.inviteDetail?.commissionRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.primary.w700.ffAkz.copyWith(fontSize: 30.gsp),
                    ),
                  ],
                ),
              ),
              Image.asset(Assets.rectangle, width: 1.gw, height: 60.gh),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'interestRateRatio'.tr(),
                      style: context.textTheme.highlight,
                    ),
                    SizedBox(height: 8.gh),
                    Text(
                      '${state.inviteDetail?.interestRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.primary.w700.ffAkz.copyWith(fontSize: 30.gsp),
                    ),
                  ],
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Divider(
            height: 1,
            color: context.theme.dividerColor,
          ),
          12.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'myCommission'.tr(),
                style: context.textTheme.primary.w500,
              ),
              Row(
                children: [
                  Text(
                    '${state.inviteDetail?.totalCommission?.toStringAsFixed(2) ?? '0.00'} CNY',
                    style: context.textTheme.regular.w500,
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14.gr,
                    color: context.colorTheme.textRegular,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showQrCodeDialog(BuildContext context, String inviteLink) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: context.theme.cardColor,
        child: Container(
          padding: EdgeInsets.all(24.gr),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              QrImageView(
                data: inviteLink,
                version: QrVersions.auto,
                size: 200.0,
                backgroundColor: context.theme.cardColor,
                padding: EdgeInsets.all(16.gr),
              ),
              SizedBox(height: 16.gh),
              Text(
                'scanQrCodeToAdd'.tr(),
                style: context.textTheme.regular.fs16.w600,
              ),
              SizedBox(height: 8.gh),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'cancel'.tr(),
                      style: TextStyle(
                        color: context.colorTheme.stockRed,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
