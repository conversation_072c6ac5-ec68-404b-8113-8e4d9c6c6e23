// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invite_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InviteState {
  DataStatus get status => throw _privateConstructorUsedError;
  InviteDetailModel? get inviteDetail => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String? get customInviteLink => throw _privateConstructorUsedError;

  /// Create a copy of InviteState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InviteStateCopyWith<InviteState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InviteStateCopyWith<$Res> {
  factory $InviteStateCopyWith(
          InviteState value, $Res Function(InviteState) then) =
      _$InviteStateCopyWithImpl<$Res, InviteState>;
  @useResult
  $Res call(
      {DataStatus status,
      InviteDetailModel? inviteDetail,
      String? error,
      String? customInviteLink});

  $InviteDetailModelCopyWith<$Res>? get inviteDetail;
}

/// @nodoc
class _$InviteStateCopyWithImpl<$Res, $Val extends InviteState>
    implements $InviteStateCopyWith<$Res> {
  _$InviteStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InviteState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? inviteDetail = freezed,
    Object? error = freezed,
    Object? customInviteLink = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      inviteDetail: freezed == inviteDetail
          ? _value.inviteDetail
          : inviteDetail // ignore: cast_nullable_to_non_nullable
              as InviteDetailModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      customInviteLink: freezed == customInviteLink
          ? _value.customInviteLink
          : customInviteLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of InviteState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InviteDetailModelCopyWith<$Res>? get inviteDetail {
    if (_value.inviteDetail == null) {
      return null;
    }

    return $InviteDetailModelCopyWith<$Res>(_value.inviteDetail!, (value) {
      return _then(_value.copyWith(inviteDetail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InviteStateImplCopyWith<$Res>
    implements $InviteStateCopyWith<$Res> {
  factory _$$InviteStateImplCopyWith(
          _$InviteStateImpl value, $Res Function(_$InviteStateImpl) then) =
      __$$InviteStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus status,
      InviteDetailModel? inviteDetail,
      String? error,
      String? customInviteLink});

  @override
  $InviteDetailModelCopyWith<$Res>? get inviteDetail;
}

/// @nodoc
class __$$InviteStateImplCopyWithImpl<$Res>
    extends _$InviteStateCopyWithImpl<$Res, _$InviteStateImpl>
    implements _$$InviteStateImplCopyWith<$Res> {
  __$$InviteStateImplCopyWithImpl(
      _$InviteStateImpl _value, $Res Function(_$InviteStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of InviteState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? inviteDetail = freezed,
    Object? error = freezed,
    Object? customInviteLink = freezed,
  }) {
    return _then(_$InviteStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      inviteDetail: freezed == inviteDetail
          ? _value.inviteDetail
          : inviteDetail // ignore: cast_nullable_to_non_nullable
              as InviteDetailModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      customInviteLink: freezed == customInviteLink
          ? _value.customInviteLink
          : customInviteLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$InviteStateImpl implements _InviteState {
  const _$InviteStateImpl(
      {this.status = DataStatus.idle,
      this.inviteDetail,
      this.error,
      this.customInviteLink});

  @override
  @JsonKey()
  final DataStatus status;
  @override
  final InviteDetailModel? inviteDetail;
  @override
  final String? error;
  @override
  final String? customInviteLink;

  @override
  String toString() {
    return 'InviteState(status: $status, inviteDetail: $inviteDetail, error: $error, customInviteLink: $customInviteLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InviteStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.inviteDetail, inviteDetail) ||
                other.inviteDetail == inviteDetail) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.customInviteLink, customInviteLink) ||
                other.customInviteLink == customInviteLink));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, status, inviteDetail, error, customInviteLink);

  /// Create a copy of InviteState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InviteStateImplCopyWith<_$InviteStateImpl> get copyWith =>
      __$$InviteStateImplCopyWithImpl<_$InviteStateImpl>(this, _$identity);
}

abstract class _InviteState implements InviteState {
  const factory _InviteState(
      {final DataStatus status,
      final InviteDetailModel? inviteDetail,
      final String? error,
      final String? customInviteLink}) = _$InviteStateImpl;

  @override
  DataStatus get status;
  @override
  InviteDetailModel? get inviteDetail;
  @override
  String? get error;
  @override
  String? get customInviteLink;

  /// Create a copy of InviteState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InviteStateImplCopyWith<_$InviteStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
