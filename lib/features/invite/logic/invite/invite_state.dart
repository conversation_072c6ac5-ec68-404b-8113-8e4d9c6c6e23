import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/invite_detail_model.dart';

part 'invite_state.freezed.dart';

@freezed
class InviteState with _$InviteState {
  const factory InviteState({
    @Default(DataStatus.idle) DataStatus status,
    InviteDetailModel? inviteDetail,
    String? error,
    String? customInviteLink,
  }) = _InviteState;
}
