import 'package:freezed_annotation/freezed_annotation.dart';

part 'invite_detail_model.freezed.dart';
part 'invite_detail_model.g.dart';

@freezed
class InviteDetailModel with _$InviteDetailModel {
  const factory InviteDetailModel({
    double? commissionRate,
    double? interestRate,
    String? inviteCode,
    double? totalCommission,
    String? customInviteLink,
  }) = _InviteDetailModel;

  factory InviteDetailModel.fromJson(Map<String, dynamic> json) => _$InviteDetailModelFromJson(json);
}
