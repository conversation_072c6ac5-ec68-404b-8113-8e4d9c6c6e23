// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invite_detail_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InviteDetailModel _$InviteDetailModelFromJson(Map<String, dynamic> json) {
  return _InviteDetailModel.fromJson(json);
}

/// @nodoc
mixin _$InviteDetailModel {
  double? get commissionRate => throw _privateConstructorUsedError;
  double? get interestRate => throw _privateConstructorUsedError;
  String? get inviteCode => throw _privateConstructorUsedError;
  double? get totalCommission => throw _privateConstructorUsedError;
  String? get customInviteLink => throw _privateConstructorUsedError;

  /// Serializes this InviteDetailModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InviteDetailModelCopyWith<InviteDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InviteDetailModelCopyWith<$Res> {
  factory $InviteDetailModelCopyWith(
          InviteDetailModel value, $Res Function(InviteDetailModel) then) =
      _$InviteDetailModelCopyWithImpl<$Res, InviteDetailModel>;
  @useResult
  $Res call(
      {double? commissionRate,
      double? interestRate,
      String? inviteCode,
      double? totalCommission,
      String? customInviteLink});
}

/// @nodoc
class _$InviteDetailModelCopyWithImpl<$Res, $Val extends InviteDetailModel>
    implements $InviteDetailModelCopyWith<$Res> {
  _$InviteDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commissionRate = freezed,
    Object? interestRate = freezed,
    Object? inviteCode = freezed,
    Object? totalCommission = freezed,
    Object? customInviteLink = freezed,
  }) {
    return _then(_value.copyWith(
      commissionRate: freezed == commissionRate
          ? _value.commissionRate
          : commissionRate // ignore: cast_nullable_to_non_nullable
              as double?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      totalCommission: freezed == totalCommission
          ? _value.totalCommission
          : totalCommission // ignore: cast_nullable_to_non_nullable
              as double?,
      customInviteLink: freezed == customInviteLink
          ? _value.customInviteLink
          : customInviteLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InviteDetailModelImplCopyWith<$Res>
    implements $InviteDetailModelCopyWith<$Res> {
  factory _$$InviteDetailModelImplCopyWith(_$InviteDetailModelImpl value,
          $Res Function(_$InviteDetailModelImpl) then) =
      __$$InviteDetailModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? commissionRate,
      double? interestRate,
      String? inviteCode,
      double? totalCommission,
      String? customInviteLink});
}

/// @nodoc
class __$$InviteDetailModelImplCopyWithImpl<$Res>
    extends _$InviteDetailModelCopyWithImpl<$Res, _$InviteDetailModelImpl>
    implements _$$InviteDetailModelImplCopyWith<$Res> {
  __$$InviteDetailModelImplCopyWithImpl(_$InviteDetailModelImpl _value,
      $Res Function(_$InviteDetailModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commissionRate = freezed,
    Object? interestRate = freezed,
    Object? inviteCode = freezed,
    Object? totalCommission = freezed,
    Object? customInviteLink = freezed,
  }) {
    return _then(_$InviteDetailModelImpl(
      commissionRate: freezed == commissionRate
          ? _value.commissionRate
          : commissionRate // ignore: cast_nullable_to_non_nullable
              as double?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      totalCommission: freezed == totalCommission
          ? _value.totalCommission
          : totalCommission // ignore: cast_nullable_to_non_nullable
              as double?,
      customInviteLink: freezed == customInviteLink
          ? _value.customInviteLink
          : customInviteLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InviteDetailModelImpl implements _InviteDetailModel {
  const _$InviteDetailModelImpl(
      {this.commissionRate,
      this.interestRate,
      this.inviteCode,
      this.totalCommission,
      this.customInviteLink});

  factory _$InviteDetailModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$InviteDetailModelImplFromJson(json);

  @override
  final double? commissionRate;
  @override
  final double? interestRate;
  @override
  final String? inviteCode;
  @override
  final double? totalCommission;
  @override
  final String? customInviteLink;

  @override
  String toString() {
    return 'InviteDetailModel(commissionRate: $commissionRate, interestRate: $interestRate, inviteCode: $inviteCode, totalCommission: $totalCommission, customInviteLink: $customInviteLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InviteDetailModelImpl &&
            (identical(other.commissionRate, commissionRate) ||
                other.commissionRate == commissionRate) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.inviteCode, inviteCode) ||
                other.inviteCode == inviteCode) &&
            (identical(other.totalCommission, totalCommission) ||
                other.totalCommission == totalCommission) &&
            (identical(other.customInviteLink, customInviteLink) ||
                other.customInviteLink == customInviteLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, commissionRate, interestRate,
      inviteCode, totalCommission, customInviteLink);

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InviteDetailModelImplCopyWith<_$InviteDetailModelImpl> get copyWith =>
      __$$InviteDetailModelImplCopyWithImpl<_$InviteDetailModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InviteDetailModelImplToJson(
      this,
    );
  }
}

abstract class _InviteDetailModel implements InviteDetailModel {
  const factory _InviteDetailModel(
      {final double? commissionRate,
      final double? interestRate,
      final String? inviteCode,
      final double? totalCommission,
      final String? customInviteLink}) = _$InviteDetailModelImpl;

  factory _InviteDetailModel.fromJson(Map<String, dynamic> json) =
      _$InviteDetailModelImpl.fromJson;

  @override
  double? get commissionRate;
  @override
  double? get interestRate;
  @override
  String? get inviteCode;
  @override
  double? get totalCommission;
  @override
  String? get customInviteLink;

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InviteDetailModelImplCopyWith<_$InviteDetailModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
