// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invite_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InviteDetailModelImpl _$$InviteDetailModelImplFromJson(
        Map<String, dynamic> json) =>
    _$InviteDetailModelImpl(
      commissionRate: (json['commissionRate'] as num?)?.toDouble(),
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      inviteCode: json['inviteCode'] as String?,
      totalCommission: (json['totalCommission'] as num?)?.toDouble(),
      customInviteLink: json['customInviteLink'] as String?,
    );

Map<String, dynamic> _$$InviteDetailModelImplToJson(
        _$InviteDetailModelImpl instance) =>
    <String, dynamic>{
      'commissionRate': instance.commissionRate,
      'interestRate': instance.interestRate,
      'inviteCode': instance.inviteCode,
      'totalCommission': instance.totalCommission,
      'customInviteLink': instance.customInviteLink,
    };
