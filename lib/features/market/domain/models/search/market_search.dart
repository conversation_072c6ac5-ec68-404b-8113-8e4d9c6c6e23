// To parse this JSON data, do
//
//     final marketSearchResponse = marketSearchResponseFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';


part 'market_search.freezed.dart';
part 'market_search.g.dart';

MarketSearchResponse marketSearchResponseFromJson(str) => MarketSearchResponse.fromJson((str));

String marketSearchResponseToJson(MarketSearchResponse data) => json.encode(data.toJson());

@freezed
class MarketSearchResponse with _$MarketSearchResponse {
  const factory MarketSearchResponse({
    int? code,
    List<MarketSearchData>? data,
    String? msg,
  }) = _MarketSearchResponse;

  factory MarketSearchResponse.fromJson(Map<String, dynamic> json) => _$MarketSearchResponseFromJson(json);
}

@freezed
class MarketSearchData with _$MarketSearchData {
  const MarketSearchData._();
  const factory MarketSearchData({
    String? currency,
    String? instrument,
    int? lotSize,
    String? market,
    String? name,
    String? securityType,
    String? symbol,
    double? tickSize,
  }) = _MarketSearchData;

  factory MarketSearchData.fromJson(Map<String, dynamic> json) => _$MarketSearchDataFromJson(json);

  String get getMarketType => '$market|$securityType|$symbol';
  Instrument get getInstrument => Instrument(instrument: getMarketType);
  /// 是否为指数
  bool get isIndex => securityType == '2';

  FTradeListItemModel toFTradeListItemModel() {
    return FTradeListItemModel()
      ..latestPrice =  0.0
      ..symbol = symbol ?? ''
      ..market = market ?? ''
      ..securityType = securityType ?? ''
      ..name = name ?? '';
  }
}
