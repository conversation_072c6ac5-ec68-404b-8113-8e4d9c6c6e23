import 'dart:async';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_service.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'f_trade_acct_position_state.dart';

class FTradeAcctPositionCubit extends Cubit<FTradeAcctPositionState> {
  FTradeAcctPositionCubit({FTradeKLineCubit? klineCubit})
      : _klineCubit = klineCubit,
        super(FTradeAcctPositionState());

  final FTradeKLineCubit? _klineCubit;
  StreamSubscription<FTradeKLineState>? _klineSub;

  Timer? _pollTimer;
  String _pollinstrument = '';
  int pageNumber = 1;
  final int _pageSize = 50;

  CancelToken? _cancelToken;

  @override
  Future<void> close() {
    _cancelToken?.cancel();
    _pollTimer?.cancel();
    _klineSub?.cancel();
    return super.close();
  }

  // 获取页码*数量 轮询所有数据
  // 总数预计在100条以下, 如果超过100条此处需要重新设计
  // 暂无法计算是否开盘
  void startPolling() {
    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) {
      _fetchListData(
        instrument: _pollinstrument,
        loadMore: false,
        pageNumber: 1,
        pageSize: pageNumber * _pageSize,
        needShowFlutterToast: false,
      );
    });
  }

  void resetPolling() {
    startPolling();
  }

  void stopPolling() {
    _pollTimer?.cancel();
  }

  Future<void> fetchData({
    required String instrument,
  }) async {
    _pollinstrument = instrument;

    emit(state.copyWith(
      status: DataStatus.loading,
    ));
    _fetchListData(instrument: instrument, loadMore: false, pageNumber: pageNumber, pageSize: _pageSize);
  }

  /// 开始同步来自 K 线的最新价到持仓记录
  void startSyncLatestPriceFromKline() {
    if (_klineSub != null) return;
    _klineSub = _klineCubit?.stream.listen(_onKlineStateUpdated);
  }

  void _onKlineStateUpdated(FTradeKLineState kState) {
    final info = kState.fTradeInfoModel;
    final model = state.orderModel;
    if (info == null || model == null) return;
    bool anyChanged = false;
    final updatedRecords = model.records.map((r) {
      if (r.market == info.market && r.securityType == info.securityType && r.symbol == info.symbol) {
        if (r.stockPrice != info.latestPrice) {
          anyChanged = true;

          // 计算浮动盈亏 (现价 - 买入均价）* 购买总量 * 杠杆倍数
          double floatingProfitLoss = (info.latestPrice - r.buyAvgPrice) * r.buyTotalNum * r.tradeUnit;
          // 计算可用保证金 购买总量 * 买入均价 * 杠杠倍数 * 保证金比例 + 浮动盈亏
          double availableMargin =
              r.buyTotalNum * r.buyAvgPrice * r.tradeUnit * r.marginRatio / 100 + floatingProfitLoss;

          return r.copyWith(
            stockPrice: info.latestPrice,
            floatingProfitLoss: floatingProfitLoss,
            availableMargin: availableMargin,
          );
        }
      }
      return r;
    }).toList();
    if (anyChanged) {
      emit(state.copyWith(status: state.dataStatus, orderModel: model.copyWith(records: updatedRecords)));
    }
  }

  Future<void> loadMoreData({
    required String instrument,
  }) async {
    pageNumber += 1;
    _fetchListData(instrument: instrument, loadMore: false, pageNumber: pageNumber, pageSize: _pageSize);
  }

  Future<void> _fetchListData({
    required String instrument,
    required bool loadMore,
    required int pageNumber,
    required int pageSize,
    bool needShowFlutterToast = true,
  }) async {
    _cancelToken?.cancel();
    _cancelToken = CancelToken();
    FTradeAcctService.fetchFTradePositionPage(
      instrument: instrument,
      pageNumber: pageNumber,
      pageSize: pageSize,
    ).then((result) {
      if (result != null) {
        if (loadMore) {
          pageNumber = result.current;
          emit(state.copyWith(
            orderModel: result.copyWith(records: (state.orderModel?.records ?? []) + result.records),
            status: DataStatus.success,
          ));
        } else {
          pageNumber = 1;
          emit(state.copyWith(
            orderModel: result,
            status: DataStatus.success,
          ));
        }

        /// 实现持仓列表刷新时，刷新「顶部期货价格」
        // 同步 latestPrice 到 FTradeKLineCubit（如果同一个 instrument）
        try {
          final match = result.records.firstWhere(
            (e) => e.instrument == _pollinstrument,
            orElse: () => FTradeAcctOrderRecords(),
          );
          if (match.symbol != 'N/A') {
            _klineCubit?.syncLatestPrice(match.stockPrice);
          }
        } catch (_) {}
      } else {
        emit(state.copyWith(
          status: DataStatus.success,
        ));
        if (needShowFlutterToast) {
          Helper.showFlutterToast(
            'Failed to fetch network error',
          );
        }
      }
    }).catchError((e) {
      emit(state.copyWith(
        status: DataStatus.failed,
      ));
      if (needShowFlutterToast) {
        Helper.showFlutterToast(
          'Failed to fetch network error',
        );
      }
    });
  }
}
