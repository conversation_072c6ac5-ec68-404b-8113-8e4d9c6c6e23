import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../../shared/constants/enums.dart';
import '../../../logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class StockWidgetHeader extends StatelessWidget {
  final MainMarketType mainMarketType;
  final RelativeRect? position;
  const StockWidgetHeader({super.key, required this.mainMarketType, this.position});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'ask'.tr(),
              style: context.textTheme.regular.fs12,
            ),
            Text(
              'bid'.tr(),
              style: context.textTheme.regular.fs12,
            ),
            PopupMenuButton<StockWidgetCount>(
              constraints: BoxConstraints.expand(width: 40.gw, height: 90.gh),
              padding: EdgeInsets.zero,
              initialValue: context.read<TradingCubit>().state.stockWidgetCount,
              color: context.theme.cardColor,
              onSelected: (StockWidgetCount item) {
                context.read<TradingCubit>().setStockWidgetCount(item);
              },
              child: BlocSelector<TradingCubit, TradingState, StockWidgetCount>(
                selector: (state) => state.stockWidgetCount,
                builder: (context, state) {
                  return _dataCell(state.value.toString(), context);
                },
              ),
              itemBuilder: (BuildContext context) => (mainMarketType.type == 'CN' || mainMarketType.type == 'HK'
                      ? StockWidgetCount.values
                      : [StockWidgetCount.one])
                  .map((e) => PopupMenuItem<StockWidgetCount>(
                        value: e,
                        child: _dataCell(e.value.toString(), context),
                      ))
                  .toList(),
            ),
          ],
        ),
      ],
    );
  }

  Container _dataCell(String value, BuildContext context) {
    return Container(
      width: 18.gw,
      height: 18.gh,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.gr),
        color: context.theme.cardColor,
        border: Border.all(width: .3, color: context.colorTheme.textPrimary),
      ),
      child: Center(
        child: Text(
          value,
          style: context.textTheme.primary.fs12.w600
              .copyWith(fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
        ),
      ),
    );
  }
}
