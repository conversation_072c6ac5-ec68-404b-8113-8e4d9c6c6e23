import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';


class NewsImage extends StatelessWidget {
  final String imageUrl;

  const NewsImage({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.gr),
      child: Container(
        color: Colors.grey[200],
        width: 120.gw,
        height: 80.gh,
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
          loadingBuilder: (_, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const _NewsImageShimmer();
          },
          errorBuilder: (_, __, ___) => const _ErrorImage(),
        ),
      ),
    );
  }
}

class _NewsImageShimmer extends StatelessWidget {
  const _NewsImageShimmer();

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      width: 120.gw,
      height: 80.gh,
    );
  }
}

class _ErrorImage extends StatelessWidget {
  const _ErrorImage();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120.gw,
      height: 80.gh,
      decoration: BoxDecoration(
        color: context.theme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.gr),
      ),
      alignment: Alignment.center,
      child: Icon(
        Icons.image_not_supported_rounded,
        color: context.theme.primaryColor.withValues(alpha: 0.3),
        size: 24.gw,
      ),
    );
  }
}
