import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';

import '../../account/logic/account/account_cubit.dart';
import '../../activity/logic/activity/activity_cubit.dart';
import '../../market/logic/market/market_cubit.dart';
import '../../notifications/logic/notifications/notifications_cubit.dart';
import '../logic/home/<USER>';
import '../logic/news/news_cubit.dart';
import '../widgets/home_banner.dart';
import '../widgets/home_market_tab.dart';
import '../widgets/home_marquee_text.dart';
import '../widgets/home_menu.dart';
import '../widgets/news_and_events.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  int _appLifecycleStatePausedTimeSecond = 0;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), () {
      getIt<IndexTradeCubit>().subscribeToTimeline();
    });
    WidgetsBinding.instance.addObserver(this);
  }

  Future<void> _initialFunction() async {
    final currentContext = context;
    if (!currentContext.mounted) return;
    getIt<IndexTradeCubit>().fetchStockConfigData();
    context.read<HomeCubit>().getBannerList();
    context.read<AccountCubit>().getContractSummary();
    context.read<NotificationsCubit>().getNotificationCount();
    context.read<MarketCubit>().fetchTableData(isHome: true);
    context.read<ActivityCubit>().getTasks();
    context.read<NewsCubit>().getNews();
  }

  @override
  void dispose() {
    getIt<IndexTradeCubit>().unsubscribeFromTimeline();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    int now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    if (state == AppLifecycleState.resumed && (now - _appLifecycleStatePausedTimeSecond) > 30) {
      // 超过 30 秒没用，回来时刷新交易时间线
      getIt<IndexTradeCubit>().reloadTimeline();
    } else if (state == AppLifecycleState.paused) {
      // 记录当前时间戳（秒）
      // 用来和下次回到前台时比较
      _appLifecycleStatePausedTimeSecond = now;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator.adaptive(
        backgroundColor: context.theme.cardColor,
        onRefresh: () async => await _initialFunction(),
        child: AnimationLimiter(
          child: ListView(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                14.verticalSpace,
                // Display row of menu buttons (Smart Investment, Sign In, etc)
                HomeMenu(),
                12.verticalSpace,
                // Display promotion banner
                HomeBanner(),
                5.verticalSpace,
                // Display marquee text
                HomeMarqueeText(),
                12.verticalSpace,
                // Display market tabs
                HomeMarketTabsSection(),
                16.verticalSpace,
                // DisplayNews and hot events
                NewsAndEvents(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
