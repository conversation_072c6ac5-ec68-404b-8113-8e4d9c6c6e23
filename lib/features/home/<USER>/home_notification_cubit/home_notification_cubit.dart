import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/home/<USER>/home_notification_cubit/home_notification_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../domain/repository/home_repository.dart';

@injectable
class HomeNotificationCubit extends Cubit<HomeNotificationState> {
  HomeNotificationCubit(this._homeRepository) : super(HomeNotificationState());
  final HomeRepository _homeRepository;

  Future<void> getNotifications({bool suppressPopup = false}) async {
    emit(state.copyWith(
      fetchStatus: DataStatus.loading,
      shouldSuppressPopup: suppressPopup,
    ));

    try {
      final result = await _homeRepository.getNotificationList();
      if (result.data != null) {
        emit(state.copyWith(
          dataList: result.data!,
          fetchStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(fetchStatus: DataStatus.success));
      }
    } catch (_) {
      emit(state.copyWith(fetchStatus: DataStatus.failed));
    }
  }
}
