import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/yhxt_news_list.dart';

import '../logic/home/<USER>';

class YhxtNewsAndEvents extends StatelessWidget {
  const YhxtNewsAndEvents({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 15.gw),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.gw),
          ),
          child: Stack(
            children: [
              Positioned.fill(child: Container(color: Colors.white,)),
              Container(
                width: 195.gw,
                height: 195.gw,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      begin: Alignment.topLeft, // 左上角
                      end: Alignment.bottomRight, // 右下角
                      colors: [
                        Color(0xFFd3e8fd), // #d2e7fc
                        Colors.white, // #ffffff
                      ],
                      stops: [
                        0,
                        0.25,
                      ]),
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 12.gw, 26.gw),
                child: Column(
                  children: [
                    _buildHeader(context),
                    10.verticalSpace,
                    // Display news and events
                    YhxtNewsList(),
                    10.verticalSpace,
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(
          "assets/images/icon_home_news.png",
          width: 24.gw,
          height: 28.gw,
        ),
        SizedBox(width: 15.gw),
        Text(
          "todayNews".tr(), // 今日资讯
          style: TextStyle(color: context.colorTheme.tabActive).fs16.w500,
        ),
      ],
    );
  }

  BorderRadius? getBorderRadius(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateD => BorderRadius.circular(0),
      _ => null,
    };
  }
}
