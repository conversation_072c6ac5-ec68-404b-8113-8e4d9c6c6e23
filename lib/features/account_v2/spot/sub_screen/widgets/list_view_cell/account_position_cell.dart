import 'dart:math';

import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:shimmer/shimmer.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AccountPositionCell extends StatelessWidget {
  final MarketCategory marketCategory;

  final FTradeAcctOrderRecords data;
  final VoidCallback onTap;
  final VoidCallback? onTapTpSL; // 止盈止损
  final VoidCallback? onTapAdd; // 追加
  final VoidCallback? onTapDetail; // 详情

  const AccountPositionCell({
    super.key,
    required this.marketCategory,
    required this.data,
    required this.onTap,
    required this.onTapTpSL,
    required this.onTapAdd,
    required this.onTapDetail,
  });

  @override
  Widget build(BuildContext context) {
    // 1. 一次性算好 TradeTypeOption 和 upColor
    final tradeOpt = TradeTypeOption.fromValue(data.tradeType);

    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gh),
      ),
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all(10.gw),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 4.gh,
                children: [
                  SymbolChip(
                    name: tradeOpt.text,
                    chipColor: tradeOpt.color(context),
                  ),
                  SymbolChip(
                    name: 'openSymbol'.tr(),
                    chipColor: context.upColor,
                  ),
                  SymbolChip(
                    name: data.market.substring(0, 2),
                    chipColor: context.theme.primaryColor,
                  ),
                  Text(
                    data.symbolName,
                    style: context.textTheme.regular.fs12.w700.copyWith(
                      color: switch (AppConfig.instance.skinStyle) {
                        AppSkinStyle.kTemplateD => context.theme.primaryColor,
                        _ => context.colorTheme.textRegular
                      },
                    ),
                  ),
                  Text(
                    '(${data.currency})',
                    style: context.textTheme.regular.fs13.w300.copyWith(
                      color: switch (AppConfig.instance.skinStyle) {
                        AppSkinStyle.kTemplateD => context.theme.primaryColor,
                        _ => context.colorTheme.textRegular
                      },
                    ),
                  ),
                  Text(
                    '(${data.id})',
                    style: context.textTheme.regular.fs13.w300.copyWith(
                      color: switch (AppConfig.instance.skinStyle) {
                        AppSkinStyle.kTemplateD => context.theme.primaryColor,
                        _ => context.colorTheme.textRegular
                      },
                    ),
                  ),
                ],
              ),

              SizedBox(height: 10.gh),

              // 第一行数据
              _PositionRow(
                configs: [
                  _FieldConfig('available'.tr(), data.restNum),
                  _FieldConfig(
                    'currentPrice'.tr(),
                    data.stockPrice,
                    isCenter: true,
                    fractionDigits: 3,
                  ),
                  _FieldConfig(
                    'floatingProfitLoss'.tr(),
                    data.floatingProfitLoss,
                    isRight: true,
                    showSymbol: true,
                    color: data.floatingProfitLoss > 0
                        ? context.upColor
                        : data.floatingProfitLoss < 0
                            ? context.downColor
                            : null,
                  ),
                ],
              ),

              SizedBox(height: 10.gh),

              Row(children: [
                
              ],)
              _PositionRow(configs: [
                _FieldConfig(
                  'floatingProfitLossRate'.tr(),
                  data.floatingProfitLossRate,
                  fractionDigits: 2,
                ),
              ]),

              // 第二行数据
              _PositionRow(
                configs: [
                  _FieldConfig('totalQuantity'.tr(), data.positionTotalNum),
                  _FieldConfig(
                    'averagePrice'.tr(),
                    data.buyAvgPrice,
                    isCenter: true,
                    fractionDigits: 3,
                  ),
                  if (marketCategory == MarketCategory.cnFutures) ...[
                    _FieldConfig(
                      'available_margin'.tr(),
                      data.availableMargin,
                      isRight: true,
                    ),
                  ] else ...[
                    _FieldConfig(
                      'marketValue'.tr(),
                      data.marketValue,
                      isRight: true,
                    ),
                  ],
                ],
              ),

              if (marketCategory == MarketCategory.cnFutures) ...[
                SizedBox(height: 20.gw),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildOperateButton("take_profit_stop_loss".tr(), onTapTpSL), // 止盈止损
                    _buildOperateButton("add_shares".tr(), onTapAdd), // 追加
                    _buildOperateButton("detail".tr(), onTapDetail), // 详情
                  ],
                ),
                SizedBox(height: 2.gw),
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOperateButton(title, onTap) {
    return CommonButton(
      title: title,
      width: 75.gw,
      height: 28.gh,
      fontSize: 14.gsp,
      radius: 6.gr,
      onPressed: onTap,
    );
  }
}

class _FieldConfig {
  final String title;
  final double value;
  final bool isRight;
  final bool isCenter;
  final bool showSymbol;
  final int fractionDigits;
  final Color? color;

  const _FieldConfig(
    this.title,
    this.value, {
    this.isRight = false,
    this.isCenter = false,
    this.showSymbol = false,
    this.fractionDigits = 2,
    this.color,
  });
}

class _PositionRow extends StatelessWidget {
  final List<_FieldConfig> configs;

  const _PositionRow({required this.configs});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: configs.map((cfg) {
        CrossAxisAlignment align = CrossAxisAlignment.start;
        if (cfg.isRight) align = CrossAxisAlignment.end;
        if (cfg.isCenter) align = CrossAxisAlignment.center;

        return Expanded(
          child: Container(
            color: Color((Random().nextDouble() * 0xFFFFFF).toInt()),
            child: Column(
              crossAxisAlignment: align,
              children: [
                Text(
                  cfg.title,
                  style: context.textTheme.regular.fs12,
                ),
                AnimatedFlipCounter(
                  fractionDigits: cfg.fractionDigits,
                  value: cfg.value,
                  prefix: cfg.showSymbol && cfg.value > 0 ? '+' : '',
                  thousandSeparator: ',',
                  textStyle: context.textTheme.primary.w700.ffAkz.copyWith(
                      color: cfg.color ??
                          switch (AppConfig.instance.skinStyle) {
                            AppSkinStyle.kTemplateD => context.colorTheme.textTitle,
                            _ => context.colorTheme.textPrimary
                          }),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}

class AccountPositionShimmerCell extends StatelessWidget {
  final MarketCategory marketCategory;

  const AccountPositionShimmerCell({
    super.key,
    required this.marketCategory,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Shimmer.fromColors(
        baseColor: context.theme.dividerColor,
        highlightColor: context.theme.cardColor.withValues(alpha: 0.5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部：tag + 标题
            Row(
              children: [
                _box(36, 15, borderRadius: 2), // tag
                const SizedBox(width: 5),
                _box(80, 15, borderRadius: 2), // 标题骨架
              ],
            ),

            const SizedBox(height: 10),

            // 中间第一行三列
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _dataColumnSkeleton(), // 可卖数量
                _dataColumnSkeleton(), // 现价
                _dataColumnSkeleton(isLast: true), // 浮动盈亏
              ],
            ),

            const SizedBox(height: 6),

            // 中间第二行三列
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _dataColumnSkeleton(), // 持仓数量
                _dataColumnSkeleton(), // 均价
                _dataColumnSkeleton(isLast: true), // 可加保证金
              ],
            ),

            if (marketCategory == MarketCategory.cnFutures) ...[
              SizedBox(height: 15.gw),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _box(75, 20, borderRadius: 4),
                  _box(75, 20, borderRadius: 4),
                  _box(75, 20, borderRadius: 4),
                ],
              ),
              SizedBox(height: 1.gw),
            ]
          ],
        ),
      ),
    );
  }

  /// 单个数据列的骨架：上下两块
  Widget _dataColumnSkeleton({bool isLast = false}) {
    return Column(
      crossAxisAlignment: isLast ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        _box(50, 15), // label 骨架
        const SizedBox(height: 3),
        _box(43, 12), // value 骨架
      ],
    );
  }

  /// 通用灰色骨架块
  Widget _box(double width, double height, {double borderRadius = 2}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
