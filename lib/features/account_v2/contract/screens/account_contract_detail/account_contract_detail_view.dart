import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/widgets/assets_card.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/account_order_list_view.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/order_table_header/order_table_header.dart';
import 'package:gp_stock_app/features/market/widgets/sliver_app_bar_delegate.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';

import '../../../../account/widgets/build_action_buttons.dart';
import 'account_contract_detail_cubit.dart';
import 'account_contract_detail_state.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

/// 合约账户详情
class AccountContractDetailPage extends StatelessWidget {
  const AccountContractDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountContractDetailCubit>();

    return BlocBuilder<AccountContractDetailCubit, AccountContractDetailState>(builder: (context, state) {
      ContractSummaryPageRecord model = state.data;
      MarketCategoryState viewModel = state.viewModel;

      final keys = viewModel.details.keys.toList();
      OrderType? selectedType;
      bool needShowTableHeader = false;
      if (viewModel.selectedIndex < keys.length) {
        selectedType = keys[viewModel.selectedIndex];
        needShowTableHeader = selectedType != OrderType.positions;
      }

      return Scaffold(
          appBar: AppBar(
            surfaceTintColor: Colors.transparent,
            backgroundColor: context.theme.cardColor,
            title: Text(getContractLabelByContractSummaryPageRecord(model)),
          ),
          body: NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                /// 资产模块
                SliverToBoxAdapter(child: _buildAssetCard(context, model)),
                if (keys.isNotEmpty)
                  SliverOverlapAbsorber(
                    handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                    sliver: SliverPersistentHeader(
                      pinned: true,
                      delegate: SliverAppBarDelegate(
                        maxHeight: needShowTableHeader ? 96 : 55,
                        minHeight: needShowTableHeader ? 96 : 55,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CommonTabBar(
                              height: 55,
                              padding: EdgeInsets.only(left: 14.gw),
                              labelPadding: EdgeInsets.symmetric(horizontal: 10.gw),
                              tabAlignment: TabAlignment.start,
                              backgroundColor: context.theme.scaffoldBackgroundColor,
                              data: viewModel.details.entries
                                  .map((e) => "${tr(e.key.nameKey)}${e.value.countIfNotEmpty}")
                                  .toList(),
                              currentIndex: viewModel.selectedIndex,
                              onTap: (index) => cubit.updateOrderViewIndex(index),
                              useNormal: true,
                            ),

                            /// 成交明细/委托明细 头部标题
                            if (needShowTableHeader) ...[
                              OrderTableHeader(type: selectedType!),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
              ];
            },
            body: DirectSlideView(
              pages: viewModel.details.entries.map((entry) {
                final type = entry.key;
                final orderListState = entry.value;
                return AccountOrderListView(
                  key: Key("${viewModel.category.nameKey}_${type.nameKey}"),
                  tradingAccountType: TradingAccountType.Contract,
                  contractModel: state.data,
                  marketCategory: viewModel.category,
                  orderType: type,
                  orderListState: orderListState,
                  onFetch: (isLoadMore) {
                    return cubit.fetchMarketOrderList(
                      type: type,
                      orderListState: orderListState,
                      isLoadMore: isLoadMore,
                    );
                  },
                );
              }).toList(),
              pageIndex: viewModel.selectedIndex,
              onPageChanged: (index) => cubit.updateOrderViewIndex(index),
            ),
          ));
    });
  }

  Widget _buildAssetCard(BuildContext context, ContractSummaryPageRecord model) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20.gr),
          bottomRight: Radius.circular(20.gr),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Assets Card
            AssetsCard(
              totalAssets: model.allAsset,
              todayEarnings: model.todayWinAmount,
              availableBalance: model.useAmount,
              myInterest: model.interestAmount,
              frozenAmount: model.freezePower,
              isContract: true,
              currency: model.currency,
              profitTitle: 'today_earnings'.tr(),
            ),
            14.verticalSpace,
            // Action Buttons
            switch (model.contractType) {
              ContractType.standard => _ActionButtons1(contractSummary: model),
              ContractType.experience => _ActionButtons2(contractSummary: model),
              ContractType.bonus => _ActionButtons3(contractSummary: model),
            },
            18.verticalSpace,
          ],
        ),
      ),
    );
  }
}

// ignore: unused_element
class _ActionButtons1 extends StatelessWidget with ContractActionHandler {
  final ContractSummaryPageRecord contractSummary;

  const _ActionButtons1({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 15.gh,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'expandMargin'.tr(),
                icon: Assets.expandMarginIcon,
                onTap: () => handleExpandMargin(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'supplementLoss'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.supplementContractIcon,
                  _ => Assets.marginCallIcon,
                },
                onTap: () => handleSupplementLoss(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'cashOut1'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.withdrawIcon,
                  _ => Assets.withdrawContractIcon,
                },
                onTap: () => handleCashOut(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'tradingCenter'.tr(),
                icon: Assets.tradingIcon,
                onTap: () => handleTradingCenter(context, contractSummary),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'fundRecords'.tr(),
                icon: Assets.recordsIcon,
                onTap: () => handleFundRecords(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'terminateContract'.tr(),
                icon: Assets.terminateContractIcon,
                onTap: () => handleTerminateContract(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'transactionHistory'.tr(),
                icon: Assets.historyIcon,
                onTap: () => handleHistory(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'contractDetails'.tr(),
                icon: Assets.contractDetailIcon,
                onTap: () => handleContractDetails(context, contractSummary),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _ActionButtons3 extends StatelessWidget with ContractActionHandler {
  final ContractSummaryPageRecord contractSummary;

  const _ActionButtons3({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 15.gh,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'expandMargin'.tr(),
                icon: Assets.expandMarginIcon,
                onTap: () => handleExpandMargin(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'supplementLoss'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.supplementContractIcon,
                  _ => Assets.myAssetIcon,
                },
                onTap: () => handleSupplementLoss(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'cashOut1'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.withdrawIcon,
                  _ => Assets.withdrawContractIcon,
                },
                onTap: () => handleCashOut(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'tradingCenter'.tr(),
                icon: Assets.tradingIcon,
                onTap: () => handleTradingCenter(context, contractSummary),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'fundRecords'.tr(),
                icon: Assets.recordsIcon,
                onTap: () => handleFundRecords(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'terminateContract'.tr(),
                icon: Assets.terminateContractIcon,
                onTap: () => handleTerminateContract(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'transactionHistory'.tr(),
                icon: Assets.historyIcon,
                onTap: () => handleHistory(context, contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'contractDetails'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.contractDetailIcon,
                  _ => Assets.withdrawIcon,
                },
                onTap: () => handleContractDetails(context, contractSummary),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _ActionButtons2 extends StatelessWidget with ContractActionHandler {
  final ContractSummaryPageRecord contractSummary;

  const _ActionButtons2({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(
          child: BuildActionButton(
            label: 'tradingCenter'.tr(),
            icon: Assets.tradingIcon,
            onTap: () => handleTradingCenter(context, contractSummary),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'apply_records'.tr(),
            icon: Assets.recordsIcon,
            onTap: () => handleFundRecords(context, contractSummary),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'terminateContract'.tr(),
            icon: Assets.terminateContractIcon,
            onTap: () => handleTerminateContract(context, contractSummary),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'historicalMessages'.tr(),
            icon: Assets.historyIcon,
            onTap: () => handleHistory(context, contractSummary),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'contractDetails'.tr(),
            icon: switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP => Assets.contractDetailIcon,
              _ => Assets.withdrawIcon,
            },
            onTap: () => handleContractDetails(context, contractSummary),
          ),
        ),
      ],
    );
  }
}

// mixin ContractActionHandler
/// 合同按钮操作处理扩展
/// Contract action tap handler extension
mixin ContractActionHandler {
  /// 跳转 扩大保证金
  /// Expand margin
  Future<void> handleExpandMargin(BuildContext context, ContractSummaryPageRecord contract) async {
    await getIt<NavigatorService>().push(AppRouter.routeMarginCall, arguments: {
      'contractId': contract.id,
      'contractActionType': ContractAction.marginExpand,
      'contractType': contract.contractType
    });
    if (!context.mounted) return;
    context.read<AccountContractDetailCubit>().fetchContractData();
  }

  /// 跳转 追加保证金
  /// Supplement loss
  void handleSupplementLoss(BuildContext context, ContractSummaryPageRecord contract) {
    getIt<NavigatorService>().push(AppRouter.routeMarginCall, arguments: {
      'contractId': contract.id,
      'contractActionType': ContractAction.replenish,
      'contractType': contract.contractType
    });
  }

  /// 跳转 合约提盈
  /// Cash out
  void handleCashOut(BuildContext context, ContractSummaryPageRecord contract) {
    getIt<NavigatorService>().push(AppRouter.routeContractWithdraw, arguments: contract.id);
  }

  /// 跳转 交易中心
  /// Navigate to trading center
  void handleTradingCenter(BuildContext context, ContractSummaryPageRecord contract) {
    final instrument = getTradingArguments(contract.marketType);
    getIt<NavigatorService>().push(AppRouter.routeTradingCenter,
      arguments: TradingArguments(
        instrumentInfo: instrument,
        selectedIndex: TradeTabType.Trading.index,
        contract: ContractSummaryData.fromRecord(contract),
        isFromContractDetails: true,
      ),
    );
  }

  /// 跳转 资金记录
  /// Fund records
  void handleFundRecords(BuildContext context, ContractSummaryPageRecord contract) {
    getIt<NavigatorService>().push(AppRouter.routeFundRecords, arguments: {'contractId': contract.id, 'isContractAccount': true});
  }

  /// 跳转 终止合约
  /// Terminate contract
  void handleTerminateContract(BuildContext context, ContractSummaryPageRecord contract) {
    getIt<NavigatorService>().push(AppRouter.routeTerminateContractV2, arguments: contract);
  }

  /// 跳转 历史订单
  /// Contract history
  void handleHistory(BuildContext context, ContractSummaryPageRecord contract) {
    getIt<NavigatorService>().push(AppRouter.routeOrderHistory, arguments: {'contractId': contract.id});
  }

  /// 跳转 合约详情
  /// Contract details
  void handleContractDetails(BuildContext context, ContractSummaryPageRecord contract) {
    AuthUtils.verifyAuth(
      () => getIt<NavigatorService>().push(AppRouter.routeContractInformationV2, arguments: contract),
    );
  }
}
