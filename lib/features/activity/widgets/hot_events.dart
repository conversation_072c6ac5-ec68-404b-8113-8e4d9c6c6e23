import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/home/<USER>/models/home_notification/home_notification_model.dart';
import 'package:gp_stock_app/features/home/<USER>/home_notification_cubit/home_notification_cubit.dart';
import 'package:gp_stock_app/features/home/<USER>/home_notification_cubit/home_notification_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:shimmer/shimmer.dart';

import '../../../shared/routes/app_router.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class HotEvents extends StatefulWidget {
  const HotEvents({super.key});

  @override
  State<HotEvents> createState() => _HotEventsState();
}

class _HotEventsState extends State<HotEvents> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeNotificationCubit, HomeNotificationState>(
      builder: (context, state) {
        if (state.fetchStatus == DataStatus.loading) {
          return const HotEventsShimmer();
        }
        if (state.fetchStatus == DataStatus.failed) {
          return Center(child: Text('failedToLoad'.tr()));
        }
        // Filter notifications by type
        final notifications = state.dataList.where((e) => e.type == 1).toList();

        if (notifications.isEmpty) {
          return const TableEmptyWidget();
        }

        return AnimationLimiter(
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) => SizedBox(height: 10.gh),
            itemCount: notifications.length,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 600),
                child: SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(
                    child: _buildNotificationItem(context, notifications[index]),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Extracted widget for individual notification item
  Widget _buildNotificationItem(BuildContext context, HomeNotificationModel notification) {
    return GestureDetector(
      onTap: () => getIt<NavigatorService>().push(AppRouter.routeEventDetails, arguments: notification),
      child: ShadowBox(
        padding: EdgeInsets.zero,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(notification.imageUrl),
            _buildContentSection(context, notification),
          ],
        ),
      ),
    );
  }

  // Extracted widget for the image section
  Widget _buildImageSection(String imageUrl) {
    return Hero(
      tag: imageUrl,
      child: Container(
        width: double.infinity,
        height: 126.gh,
        decoration: BoxDecoration(
          color: context.theme.primaryColor.withValues(alpha: 0.05),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.all(
            Radius.circular(4),
          ),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: imageUrl.isEmpty
                ? const Icon(Icons.image, color: Colors.grey)
                : CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) => const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  // Extracted widget for the content section
  Widget _buildContentSection(BuildContext context, HomeNotificationModel notification) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.gh, vertical: 8.gh),
      child: Text(
        notification.title,
        style: context.textTheme.primary,
      ),
    );
  }
}

class HotEventsShimmer extends StatelessWidget {
  final int itemCount;

  const HotEventsShimmer({
    super.key,
    this.itemCount = 3,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: context.theme.dividerColor,
      highlightColor: context.theme.cardColor.withValues(alpha: 0.5),
      child: ListView.separated(
        separatorBuilder: (context, index) => SizedBox(height: 10.gh),
        itemCount: itemCount,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Shimmer for Image
              Container(
                width: 347.gw,
                height: 126.gh,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.gr),
                ),
              ),
              SizedBox(height: 10.gh),
              // Shimmer for Title
              Container(
                width: 200.gw,
                height: 16.gh,
                color: Colors.white,
                margin: EdgeInsets.symmetric(horizontal: 12.gh),
              ),
            ],
          );
        },
      ),
    );
  }
}
