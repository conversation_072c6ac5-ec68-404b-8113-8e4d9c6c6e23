import 'package:intl/intl.dart';

class ConvertHelper {
  static String formatDate(String dateString) {
    DateTime dateTime = DateTime.parse(dateString);
    DateFormat formatter = DateFormat("MMM d, yyyy h:mm a");
    String formattedDate = formatter.format(dateTime);
    return formattedDate;
  }

  static String formatDateType1(
    String dateString, {
    String separator = '/',
    bool showSeconds = false,
  }) {
    DateTime dateTime = DateTime.parse(dateString);
    DateFormat formatter = DateFormat("yyyy${separator}MM${separator}dd ${showSeconds ? 'HH:mm:ss' : 'HH:mm'}");
    String formattedDate = formatter.format(dateTime);
    return formattedDate;
  }

  static String formatDateTypeIn24Hour(String dateString, {String separator = '/'}) {
    DateTime dateTime = DateTime.parse(dateString);
    DateFormat formatter = DateFormat("yyyy${separator}MM${separator}dd HH:mm");
    String formattedDate = formatter.format(dateTime);
    return formattedDate;
  }

  static String formatPriceUsd(double price) {
    String value;
    if (price < 0 && price > -1 || price > 0 && price < 1) {
      value = formatNumberWithFourDecimals(price);
    } else {
      value = formatNumberWithTwoDecimals(price);
    }
    final NumberFormat nfFour = NumberFormat('#,##0.0000', 'en_US');
    final NumberFormat nfTwo = NumberFormat('#,##0.00', 'en_US');
    double val = double.parse(value);
    if (val == 0) {
      return '\$0.00';
    } else if (val >= 1) {
      return '\$${nfTwo.format(val.abs())}';
    } else if (val > 0 && val < 0.0001) {
      return '\$0.00';
    } else if (val < 0 && val > -0.0001) {
      return '\$0.00';
    } else if (val > 0 && val < 1) {
      return '\$${nfFour.format(val.abs())}';
    } else if (val < 0 && val > -1) {
      return '-\$${nfFour.format(val.abs())}';
    } else if (val <= -1) {
      return '-\$${nfTwo.format(val.abs())}';
    } else {
      return '\$${val.abs()}';
    }
  }

  //Jan, 1
  static String formatDateMonthDay(String dts) {
    DateTime? dt = DateTime.tryParse(dts);
    if (dt == null) return '';
    final DateFormat fm = DateFormat('MMM');
    String m = fm.format(dt);
    return '$m, ${dt.day}';
  }

  //Jan
  static String formatMonth(String dts) {
    DateTime? dt = DateTime.tryParse(dts);
    if (dt == null) return '';
    final DateFormat fm = DateFormat('MMM');
    return fm.format(dt);
  }

  //2020-01-01
  static String formatDateGeneral(String dts) {
    DateTime? dt = DateTime.tryParse(dts);
    if (dt == null) return '';
    final DateFormat fm = DateFormat('yyyy-MM-dd');
    return fm.format(dt);
  }

  //00-00-00
  static String formatTimeGeneral(String dts) {
    DateTime? dt = DateTime.tryParse(dts);
    if (dt == null) return '';
    final DateFormat fm = DateFormat('HH:mm:ss');
    return fm.format(dt);
  }

  static String formatNumberWithTwoDecimals(num number) {
    var truncatedNumber = number.toStringAsFixed(3);
    final decimalIndex = truncatedNumber.indexOf('.');
    if (decimalIndex != -1) {
      final decimalPlaces = truncatedNumber.substring(decimalIndex + 1);
      if (decimalPlaces.length > 2) {
        truncatedNumber = truncatedNumber.substring(0, decimalIndex + 3);
      }
    }
    return truncatedNumber;
  }

  static String formatNumberWithFourDecimals(num number) {
    if ((number > 0 && number < 0.001) || (number < 0 && number > -0.001)) {
      return number.toString();
    }
    var truncatedNumber = number.toStringAsFixed(5);
    final decimalIndex = truncatedNumber.indexOf('.');
    if (decimalIndex != -1) {
      final decimalPlaces = truncatedNumber.substring(decimalIndex + 1);
      if (decimalPlaces.length > 4) {
        truncatedNumber = truncatedNumber.substring(0, decimalIndex + 5);
      }
    }
    return truncatedNumber;
  }
}
