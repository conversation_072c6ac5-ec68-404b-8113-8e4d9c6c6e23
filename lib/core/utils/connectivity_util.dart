import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'log.dart';

class NetworkStatus {
  final bool hasConnection;
  final bool isWifi;

  NetworkStatus({
    required this.hasConnection,
    required this.isWifi,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is NetworkStatus &&
              runtimeType == other.runtimeType &&
              hasConnection == other.hasConnection &&
              isWifi == other.isWifi;

  @override
  int get hashCode => hasConnection.hashCode ^ isWifi.hashCode;
}

class ConnectivityUtil {
  static final ConnectivityUtil _instance = ConnectivityUtil._internal();
  factory ConnectivityUtil() => _instance;
  ConnectivityUtil._internal();

  final _connectivityController = StreamController<NetworkStatus>.broadcast();
  Stream<NetworkStatus> get connectivityStream => _connectivityController.stream;

  NetworkStatus _status = NetworkStatus(hasConnection: false, isWifi: false);
  NetworkStatus get status => _status;

  late StreamSubscription<List<ConnectivityResult>> _subscription;

  void initialize() {
    _subscription = Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) {
      _updateConnectionStatus(results);
    });

    // 检查初始状态
    Connectivity().checkConnectivity().then((results) {
      _updateConnectionStatus(results);
    });
  }

  void _updateConnectionStatus(List<ConnectivityResult> results) {
    final hasConnection = results.any((result) => result != ConnectivityResult.none);
    final isWifi = results.contains(ConnectivityResult.wifi);

    final newStatus = NetworkStatus(
      hasConnection: hasConnection,
      isWifi: isWifi,
    );

    LogD("当前网络状态 >>>> hasConnection: $hasConnection, isWifi: $isWifi");

    if (_status != newStatus) {
      _status = newStatus;
      _connectivityController.add(newStatus);
    }
  }

  void dispose() {
    _subscription.cancel();
    _connectivityController.close();
  }
}