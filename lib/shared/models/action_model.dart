import '../../features/main/domain/enums/navigation_item.dart';

enum ActionType {
  navigate,
  switchTab,
  switchTabAndNavigate,
}

class ActionModel {
  final ActionType actionType;
  final BottomNavType? bottomBarIndex;
  final int? tabIndex;
  final String? routeName;

  const ActionModel({this.actionType = ActionType.navigate, this.bottomBarIndex, this.tabIndex, this.routeName})
      : assert((actionType == ActionType.switchTab && bottomBarIndex != null && tabIndex != null) ||
            (actionType == ActionType.navigate && bottomBarIndex == null && tabIndex == null) ||
            (actionType == ActionType.switchTabAndNavigate &&
                bottomBarIndex != null &&
                tabIndex != null &&
                routeName != null));
}
