import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit_channel/deposit_channel_cubit.dart';
import 'package:gp_stock_app/features/account/logic/otp/otp_cubit.dart';
import 'package:gp_stock_app/features/account/logic/pay_order/pay_order_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/screens/deposit_main_screen.dart';
import 'package:gp_stock_app/features/account/screens/deposit_record_screen.dart';
import 'package:gp_stock_app/features/account/screens/deposit_screen.dart';
import 'package:gp_stock_app/features/account/screens/history/spot_and_contract_history_screen.dart';
import 'package:gp_stock_app/features/account/screens/order_detail_screen.dart';
import 'package:gp_stock_app/features/account/screens/pay_order/pay_order_screen.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/trading_center_screen.dart';
import 'package:gp_stock_app/features/account/screens/transfer_type_screen.dart';
import 'package:gp_stock_app/features/account/screens/withdraw_main_screen.dart';
import 'package:gp_stock_app/features/account/screens/withdraw_screen.dart';
import 'package:gp_stock_app/features/account/screens/withdrawal_record_screen.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/1_common/order_history/order_history_cubit.dart';
import 'package:gp_stock_app/features/account_v2/1_common/order_history/order_history_view.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/account_contract_detail_cubit.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/account_contract_detail_view.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/contract_info_v2/contract_info_v2_cubit.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/contract_info_v2/contract_info_v2_view.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/terminate_contract/terminate_contract_cubit.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/terminate_contract/terminate_contract_screen.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/apply_normal_contract/contract_application_screen.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/apply_trial_contract/apply_trial_contract_cubit.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/apply_trial_contract/apply_trial_contract_page.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/contract_withdraw/contract_withdraw_cubit.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/contract_withdraw/contract_withdraw_screen.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/expand_margin/expand_margin_cubit.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/expand_margin/expand_margin_screen.dart';
import 'package:gp_stock_app/features/account_v2/spot/account_position_detail/account_position_detail_cubit.dart';
import 'package:gp_stock_app/features/account_v2/spot/account_position_detail/account_position_detail_view.dart';
import 'package:gp_stock_app/features/company_news/logic/news/company_news_cubit.dart';
import 'package:gp_stock_app/features/company_news/screens/company_news_screen.dart';
import 'package:gp_stock_app/features/contract/logic/contract/contract_cubit.dart';
import 'package:gp_stock_app/features/contract/logic/fund_records/fund_records_cubit.dart';
import 'package:gp_stock_app/features/contract/screens/Interest_records/Interest_records_screen.dart';
import 'package:gp_stock_app/features/contract/screens/apply_records/contract_apply_records.dart';
import 'package:gp_stock_app/features/contract/screens/apply_records/contract_apply_screen.dart';
import 'package:gp_stock_app/features/contract/screens/apply_records/contract_settle_history_screen.dart';
import 'package:gp_stock_app/features/contract/screens/fund_records/fund_records_screen.dart';
import 'package:gp_stock_app/features/convert_rate/logic/convert_rate/convert_rate_cubit.dart';
import 'package:gp_stock_app/features/convert_rate/screens/convert_rate_screen.dart';
import 'package:gp_stock_app/features/forgot/logic/forgot/forgot_cubit.dart';
import 'package:gp_stock_app/features/forgot/screens/forgot_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/models/home_notification/home_notification_model.dart'
    show HomeNotificationModel;
import 'package:gp_stock_app/features/home/<USER>/models/news/news_model.dart';
import 'package:gp_stock_app/features/home/<USER>/ai_chat/ai_chat_cubit.dart' show AIChatCubit;
import 'package:gp_stock_app/features/home/<USER>/ai_chat_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/events/event_details_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/news/news_details_screen.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_cubit.dart';
import 'package:gp_stock_app/features/invite/screens/invite_screen.dart';
import 'package:gp_stock_app/features/main/screens/main_screen.dart';
import 'package:gp_stock_app/features/market/domain/models/plate_info_request/plate_info_request.dart';
import 'package:gp_stock_app/features/market/logic/index_page/index_page_cubit.dart';
import 'package:gp_stock_app/features/market/plate_info_screen.dart';
import 'package:gp_stock_app/features/market/plate_list_screen.dart';
import 'package:gp_stock_app/features/market/screens/index_page_screen.dart';
import 'package:gp_stock_app/features/notifications/screens/notification_list_screen.dart';
import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/help/help_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/vip/vip_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/about_us/about_us_screen.dart';
import 'package:gp_stock_app/features/profile/screens/about_us/app_info_content_screen.dart';
import 'package:gp_stock_app/features/profile/screens/auth_n/auth_n_screen.dart';
import 'package:gp_stock_app/features/profile/screens/avatar/avatar_screen.dart';
import 'package:gp_stock_app/features/profile/screens/mobile/update_mobile_screen.dart';
import 'package:gp_stock_app/features/profile/screens/password/change_password_phone_screen.dart';
import 'package:gp_stock_app/features/profile/screens/password/change_password_screen.dart';
import 'package:gp_stock_app/features/profile/screens/password/password_settings_screen.dart';
import 'package:gp_stock_app/features/profile/screens/questions/questions_details.dart';
import 'package:gp_stock_app/features/profile/screens/questions/questions_screen.dart';
import 'package:gp_stock_app/features/profile/screens/settings/mission_center_screen.dart';
import 'package:gp_stock_app/features/profile/screens/settings/personal_info_edit.dart';
import 'package:gp_stock_app/features/profile/screens/settings/settings_screen.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/add_wallet_screen.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/third_party_channel_screen.dart';
import 'package:gp_stock_app/features/sign_in/screens/sign_in_screen.dart';
import 'package:gp_stock_app/features/sign_up/logic/sign_up/sign_up_cubit.dart';
import 'package:gp_stock_app/features/sign_up/screens/sign_up_screen.dart';
import 'package:gp_stock_app/features/splash/screens/splash_screen.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';

import 'irouter_provider.dart';




class AppRouter extends IRouterProvider {

  static const String splash = '/';
  static const String routeLogin = '/login';
  static const String routeSignUp = '/login/sign-up';
  static const String routeForgot = '/forgot';
  static const String routeMain = '/main';
  static const String routeAboutUs = '/about-us';
  static const String routeSettings = '/settings';
  static const String routePassword = '/password';
  static const String routeChangePasswordWithPhone = '/change-phone';
  static const String routeChangePassword = '/change-password';
  static const String routeUpdateMobile = '/update-mobile';
  static const String routePersonalInfoEdit = '/personal-info-edit';
  static const String routeAuthN = '/auth';
  static const String routeQuestions = '/questions';
  static const String routeQuestionsDetails = '/questions-details';
  static const String routeCompanyNewsDetails = '/company-news-details';
  static const String routeContract = '/contract';
  static const String routeContractDetails = '/contract-details';
  static const String routeContractDetail = '/account/contract/detail';
  static const String routeContractApply = '/contract-apply';
  static const String routeContractApplication = '/contract-application';
  static const String routeContractSettleHistory = '/contract-settle-history';
  static const String routeContractActivity = '/contract-activity';
  static const String routeAccountDetails = '/account-details';
  static const String routeAvatarScreen = '/avatar-screen';
  static const String routeContractApplyRecord = '/contract-apply-record';
  static const String routeSpotAndContractHistory = '/spot-&-contract-history';
  static const String routeOrderHistory = '/order_history';
  static const String routeNotification = '/notification';
  static const String routeNotificationList = '/notification-list';
  static const String routeTradingCenter = '/trading-center';
  static const String routeTerminateContract = '/terminate-contract';
  static const String routeTerminateContractV2 = '/contract/terminate_v2';
  static const String routeContractInformationV2 = '/contract/info_v2';
/*
  ============================================================================================================================
  期货交易 Futures trade
  ============================================================================================================================
  */
  /// 期货市场完整信息页面
  static const String routeFTradeAllInfo = '/futures-trade-allinfo';

/*
  ============================================================================================================================
  钱包 Wallet
  ============================================================================================================================
  */
  static const String routeDeposit = '/deposit';
  static const String routeWithdraw = '/withdraw';
  static const String routeTransferType = '/transfer-type';
  static const String routeMissionCenter = '/mission-center';
  static const String routePlateInfo = '/plate-info';
  static const String routeFundRecords = '/fund-records';
  static const String routeInterestRecord = '/interset-records';
  static const String routeMarginCall = '/margin-call';
  static const String routePlateList = '/plate-list';
  static const String routeInvite = '/invite';
  static const String routeWithdrawRecords = '/withdraw-records';
  static const String routeDepositRecords = '/deposit-records';
  static const String routeContractWithdraw = '/contract-withdraw';
  static const String routeNewsDetails = '/news-details';
  static const String routeAppInfoContent = '/app-info-content';
  static const String routeEventDetails = '/event-details';
  static const String routeAIChat = '/ai-chat';
  static const String routeConvertRate = '/convert-rate';
  static const String routeThirdPartyChannel = '/third-party-channel';
  static const String routeAddWallet = '/add-wallet';
  static const String routeIndexPage = '/index-page';
  static const String routeDepositMain = '/deposit-main';
  static const String routePayOrder = '/pay-order';
  static const String routeWithdrawMain = '/withdraw-main';
  static const String routeOrderDetail = '/order-detail';
  static const String routeSpotPositionDetail = '/spot/order/detail';

  @override
  void initRouter(FluroRouter router) {
    // 根路由
    router.define(splash, handler: Handler(handlerFunc: (context, params) {
      return SplashScreen();
    }));

    // 登录路由
    router.define(routeLogin, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>?;
      final needRedirection = arguments?['needRedirection'] ?? false;
      final isSignUp = arguments?['isSignUp'] ?? false;
      return SignInScreen(
        isSignUp: isSignUp,
        needRedirection: needRedirection,
      );
    }));

    // 注册路由
    router.define(routeSignUp, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => SignUpCubit(),
        child: SignUpScreen(),
      );
    }));

    // 忘记密码路由
    router.define(routeForgot, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => getIt<ForgotCubit>(),
        child: ForgotScreen(),
      );
    }));

    // 主页路由
    router.define(routeMain, handler: Handler(handlerFunc: (context, params) {
      return MainScreen();
    }));

    // 关于我们路由
    router.define(routeAboutUs, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider.value(
        value: getIt<AppInfoCubit>(),
        child: AboutUsScreen(),
      );
    }));

    // 设置路由
    router.define(routeSettings, handler: Handler(handlerFunc: (context, params) {
      return SettingsScreen();
    }));

    // 密码设置路由
    router.define(routePassword, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final type = arguments['type'] as PasswordModifyType;
      return PasswordSettingsScreen(type: type);
    }));

    // 手机验证修改密码路由
    router.define(routeChangePasswordWithPhone, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final type = arguments['type'] as PasswordModifyType;
      return BlocProvider(
        create: (context) => getIt<OtpCubit>(),
        child: ChangePasswordWithPhoneScreen(type: type),
      );
    }));

    // 修改密码路由
    router.define(routeChangePassword, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final type = arguments['type'] as PasswordModifyType;
      return ChangePasswordScreen(type: type);
    }));

    // 更新手机号路由
    router.define(routeUpdateMobile, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => getIt<OtpCubit>(),
        child: UpdateMobileScreen(),
      );
    }));

    // 个人信息编辑路由
    router.define(routePersonalInfoEdit, handler: Handler(handlerFunc: (context, params) {
      return PersonalInfoEditScreen();
    }));

    // 认证路由
    router.define(routeAuthN, handler: Handler(handlerFunc: (context, params) {
      return AuthNScreen();
    }));

    // 问题列表路由
    router.define(routeQuestions, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => getIt<HelpCubit>()..getHelpList(),
        child: QuestionsScreen(),
      );
    }));

    // 问题详情路由
    router.define(routeQuestionsDetails, handler: Handler(handlerFunc: (context, params) {
      final questionId = context?.settings?.arguments as int;
      return BlocProvider(
        create: (_) => getIt<HelpCubit>()..getHelpQuestionDetail(questionId.toString()),
        child: QuestionsDetailsScreen(questionId: questionId),
      );
    }));

    // 公司新闻详情路由
    router.define(routeCompanyNewsDetails, handler: Handler(handlerFunc: (context, params) {
      final articleId = context?.settings?.arguments as String;
      return BlocProvider(
        create: (_) => getIt<CompanyNewsCubit>()..getCompanyNewsDetails(articleId),
        child: CompanyNewsDetailsScreen(newsId: articleId),
      );
    }));
    // 合约详情路由
    router.define(routeContractDetail, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      return BlocProvider(
        create: (context) => AccountContractDetailCubit(
          data: arguments['model'],
        ),
        child: AccountContractDetailPage(),
      );
    }));

    // 合约申请路由
    router.define(routeContractApply, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final mainContractType = arguments['mainContractType'] as InstrumentType;
      return BlocProvider(
        create: (context) => getIt<ContractCubit>()..getOpenContractType(),
        child: ContractApplyScreen(mainContractType: mainContractType),
      );
    }));

    // 合约申请表单路由
    router.define(routeContractApplication, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final mainContractType = arguments['mainContractType'] as InstrumentType;
      final contractType = arguments['contractType'] as ContractType;
      return BlocProvider(
        create: (context) => getIt<ContractCubit>(),
        child: ContractApplicationScreen(
          mainContractType: mainContractType,
          contractType: contractType,
        ),
      );
    }));

    // 合约活动路由
    router.define(routeContractActivity, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final contractType = arguments['contractType'] as ContractType;
      final mainContractType = arguments['mainContractType'] as InstrumentType;
      return BlocProvider(
        create: (context) => ApplyTrialContractCubit(),
        child: ApplyTrialContractPage(contractType: contractType, mainContractType: mainContractType),
      );
    }));

    // 现货和合约历史路由
    router.define(routeSpotAndContractHistory, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments is Map<String, dynamic>
          ? context?.settings?.arguments as Map<String, dynamic>
          : {'contract': null};
      final contract = arguments['contract'];
      return SpotAndContractHistoryScreen(contract: contract);
    }));

    /// 历史订单路由
    router.define(routeOrderHistory, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>;
      final category = arguments['category'] as MarketCategory?;
      final contractId = arguments['contractId'] as int?;
      return BlocProvider(
        create: (context) => OrderHistoryCubit(
          category: category,
          contractId: contractId,
        ),
        child: OrderHistoryPage(),
      );
    }));

    // 头像设置路由
    router.define(routeAvatarScreen, handler: Handler(handlerFunc: (context, params) {
      return AvatarScreen();
    }));

    // 合约申请记录路由
    router.define(routeContractApplyRecord, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => getIt<ContractCubit>(),
        child: ContractApplyRecordsScreen(),
      );
    }));

    // 合约结算历史路由
    router.define(routeContractSettleHistory, handler: Handler(handlerFunc: (context, params) {
      return ContractSettleHistoryScreen();
    }));

    // 通知列表路由
    router.define(routeNotificationList, handler: Handler(handlerFunc: (context, params) {
      return NotificationListScreen();
    }));
    // 交易中心路由
    router.define(routeTradingCenter, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as TradingArguments;
      final selectedIndex = arguments.selectedIndex;
      final isFromHome = arguments.shouldNavigateToIndex;
      final instrument = arguments.instrumentInfo;
      final isIndexTrading = arguments.isIndexTrading;
      final contract = arguments.contract;
      final tradeType = arguments.tradeType;
      final isFromContractDetails = arguments.isFromContractDetails;
      final tradingCubit = TradingCubit();
      return MultiBlocProvider(
        providers: [
          BlocProvider(
              create: (context) => tradingCubit
                ..setContract(contract, isFromContractDetails: isFromContractDetails)
                ..setTradingType(tradeType ?? getContractLabel(contract))
                ..setIndexTrading(isIndexTrading: isIndexTrading)
                ..setContractId(contract?.id)),
          BlocProvider(
            create: (context) => getIt<AccountCubit>()..startTradingCenterPolling(instrument, contract?.id),
          ),
        ],
        child: TradingCenterScreen(
          instrument: instrument,
          selectedIndex: selectedIndex,
          shouldNavigateToIndex: isFromHome,
          isIndexTrading: isIndexTrading,
        ),
      );
    }));

    // 终止合约路由
    router.define(routeTerminateContract, handler: Handler(handlerFunc: (context, params) {
      final contractSummary = context?.settings?.arguments as ContractSummaryData;
      return TerminateContractPage();
    }));

    // 终止合约V2路由
    router.define(routeTerminateContractV2, handler: Handler(handlerFunc: (context, params) {
      final model = context?.settings?.arguments as ContractSummaryPageRecord;
      return BlocProvider(
        create: (context) => TerminateContractCubit(model: model),
        child: TerminateContractPage(),
      );
    }));

    // 合约信息V2路由
    router.define(routeContractInformationV2, handler: Handler(handlerFunc: (context, params) {
      final model = context?.settings?.arguments as ContractSummaryPageRecord;
      return BlocProvider(
        create: (context) => ContractInfoV2Cubit(data: model),
        child: ContractInfoV2Page(),
      );
    }));

    // 期货交易全信息路由
    router.define(routeFTradeAllInfo, handler: Handler(handlerFunc: (context, params) {
      assert(context?.settings?.arguments != null, 'arguments can not be null');
      return FTradeAllInfoScreen.fromArgs(context?.settings?.arguments);
    }));

    // 充值路由
    router.define(routeDeposit, handler: Handler(handlerFunc: (context, params) {
      return MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => BankListCubit(),
          ),
          BlocProvider(
            create: (context) => UserBankListCubit()..fetchBankList(),
          ),
          BlocProvider(
            create: (context) => DepositCubit(),
          ),
        ],
        child: DepositScreen(),
      );
    }));

    // 提现路由
    router.define(routeWithdraw, handler: Handler(handlerFunc: (context, params) {
      return MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => BankListCubit(),
          ),
          BlocProvider(
            create: (context) => UserBankListCubit()..fetchBankList(),
          ),
          BlocProvider(
            create: (context) => WithdrawalCubit()..getConfig(),
          ),
        ],
        child: WithdrawScreen(),
      );
    }));

    // 转账类型路由
    router.define(routeTransferType, handler: Handler(handlerFunc: (context, params) {
      final transferType = context?.settings?.arguments as TransferType;
      return MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => DepositChannelCubit(),
          ),
        ],
        child: TransferTypeScreen(type: transferType),
      );
    }));

    // 任务中心路由
    router.define(routeMissionCenter, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as bool?;
      final isVip = arguments ?? false;
      return BlocProvider(
        create: (context) => getIt<VipCubit>(),
        child: MissionCenterScreen(isVip: isVip),
      );
    }));
    // 板块信息路由
    router.define(routePlateInfo, handler: Handler(handlerFunc: (context, params) {
      final plateInfoRequest = context?.settings?.arguments as PlateInfoRequest;
      return PlateInfoScreen(plateInfoRequest: plateInfoRequest);
    }));
    // 资金记录路由
    router.define(routeFundRecords, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments is Map<String, dynamic>
          ? context?.settings?.arguments as Map<String, dynamic>
          : {'contractId': null};
      final contractId = arguments['contractId'];
      final isContractAccount = arguments['isContractAccount'] ?? false;
      return BlocProvider(
        create: (context) => getIt<FundRecordsCubit>(),
        child: FundRecordsScreen(contractId: contractId, isContractAccount: isContractAccount),
      );
    }));
    // 利息记录路由
    router.define(routeInterestRecord, handler: Handler(handlerFunc: (context, params) {
      return InterestRecordsScreen();
    }));

    // 追加保证金路由
    router.define(routeMarginCall, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments is Map<String, dynamic>
          ? context?.settings?.arguments as Map<String, dynamic>
          : {'contractId': null, 'contractType': ContractAction.replenish};
      final contractId = arguments['contractId'];
      final contractActionType = arguments['contractActionType'];
      final contractType = arguments['contractType'];
      return BlocProvider(
        create: (context) => ExpandMarginCubit(
          contractId: contractId,
          contractActionType: contractActionType,
          contractType: contractType,
        ),
        child: ExpandMarginScreen(),
      );
    }));

    // 板块列表路由
    router.define(routePlateList, handler: Handler(handlerFunc: (context, params) {
      return PlateListScreen();
    }));

    // 邀请路由
    router.define(routeInvite, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => getIt<InviteCubit>(),
        child: InviteScreen(),
      );
    }));

    // 提现记录路由
    router.define(routeWithdrawRecords, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => WithdrawalCubit(),
        child: WithdrawalRecordsScreen(),
      );
    }));

    // 充值记录路由
    router.define(routeDepositRecords, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => DepositCubit(),
        child: DepositRecordsScreen(),
      );
    }));

    // 支付订单路由
    router.define(routePayOrder, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => PayOrderCubit(),
        child: const PayOrderScreen(),
      );
    }));

    // 合约提现路由
    router.define(routeContractWithdraw, handler: Handler(handlerFunc: (context, params) {
      final contractId = context?.settings?.arguments as int;
      return BlocProvider(
        create: (context) => ContractWithdrawCubit(contractId),
        child: ContractWithdrawScreen(),
      );
    }));

    // 新闻详情路由
    router.define(routeNewsDetails, handler: Handler(handlerFunc: (context, params) {
      final NewsItem news = context?.settings?.arguments as NewsItem;
      return NewsDetailsScreen(news: news);
    }));

    // 应用信息内容路由
    router.define(routeAppInfoContent, handler: Handler(handlerFunc: (context, params) {
      final appInfo = context?.settings?.arguments as AppInfoModel;
      return AppInfoContentScreen(appInfo: appInfo);
    }));

    // 事件详情路由
    router.define(routeEventDetails, handler: Handler(handlerFunc: (context, params) {
      final event = context?.settings?.arguments as HomeNotificationModel;
      return EventDetailsScreen(event: event);
    }));

    // AI聊天路由
    router.define(routeAIChat, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AIChatCubit(),
        child: AIChatScreen(),
      );
    }));

    // 汇率转换路由
    router.define(routeConvertRate, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => getIt<ConvertRateCubit>()..fetchConvertRate(),
        child: ConvertRateScreen(),
      );
    }));

    // 第三方渠道路由
    router.define(routeThirdPartyChannel, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>?;
      final transactionType = arguments?['type'] as ThirdPartyTransactionType? ?? ThirdPartyTransactionType.deposit;
      return BlocProvider(
        create: (context) => getIt<ThirdPartyChannelCubit>(),
        child: ThirdPartyChannelScreen(
          transactionType: transactionType,
        ),
      );
    }));

    // 添加钱包路由
    router.define(routeAddWallet, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider.value(
        value: getIt<ThirdPartyChannelCubit>()..fetchSupportChannelList(),
        child: AddWalletScreen(),
      );
    }));

    // 指数页面路由
    router.define(routeIndexPage, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>?;
      final isTransactionDetail = arguments?['isTransactionDetail'] as bool? ?? false;
      final isFromTimedLiquidation = arguments?['isFromTimedLiquidation'] as bool? ?? false;
      return BlocProvider(
        create: (_) => getIt<IndexPageCubit>(),
        child: IndexPageScreen(
          isTransactionDetail: isTransactionDetail,
            isFromTimedLiquidation: isFromTimedLiquidation,
        ),
      );
    }));

    // 充值主页路由
    router.define(routeDepositMain, handler: Handler(handlerFunc: (context, params) {
      final arguments = context?.settings?.arguments as Map<String, dynamic>?;
      final depositType = arguments?['type'] as DepositType? ?? DepositType.bank;
      return MultiBlocProvider(
        providers: [
          BlocProvider<DepositCubit>(create: (context) => DepositCubit()),
          BlocProvider<UserBankListCubit>(create: (context) => UserBankListCubit()..fetchBankList()),
          BlocProvider<BankListCubit>(create: (context) => BankListCubit()),
          BlocProvider(create: (context) => getIt<DepositChannelCubit>()..getDepositChannelsList()),
        ],
        child: DepositMainScreen(depositType: depositType),
      );
    }));

    // 提现主页路由
    router.define(routeWithdrawMain, handler: Handler(handlerFunc: (context, params) {
      return MultiBlocProvider(
        providers: [
          BlocProvider<WithdrawalCubit>(create: (context) => WithdrawalCubit()..getConfig()),
          BlocProvider<UserBankListCubit>(create: (context) => UserBankListCubit()..fetchBankList()),
          BlocProvider<BankListCubit>(create: (context) => BankListCubit()),
        ],
        child: WithdrawMainScreen(),
      );
    }));

    // 订单详情路由
    router.define(routeOrderDetail, handler: Handler(handlerFunc: (context, params) {
      final args = context?.settings?.arguments as Map<String, dynamic>;
      final orderId = args['orderId'];
      return BlocProvider(
        create: (context) => getIt<AccountCubit>(),
        child: OrderDetailScreen(orderId: orderId),
      );
    }));

    // 现货持仓详情路由
    router.define(routeSpotPositionDetail, handler: Handler(handlerFunc: (context, params) {
      final args = context?.settings?.arguments as Map<String, dynamic>;
      final model = args['contractModel'];
      final id = args['id'];
      return BlocProvider(
        create: (context) => AccountPositionDetailCubit(model: model, id: id),
        child: AccountPositionDetailView(),
      );
    }));

    // 默认路由处理
    router.notFoundHandler = Handler(handlerFunc: (context, params) {
      return Scaffold(
        body: Center(child: Text('No route defined for ${context?.settings?.name}')),
      );
    });
  }

}
