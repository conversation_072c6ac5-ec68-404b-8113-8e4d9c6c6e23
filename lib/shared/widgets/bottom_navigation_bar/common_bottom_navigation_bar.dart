import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';

class CommonBottomNavigationBar extends StatelessWidget {
  final List<BottomNavConfig> data;
  final ValueChanged<BottomNavType> onTabSwitch;
  final BottomNavType currentTabType;

  const CommonBottomNavigationBar({
    super.key,
    required this.data,
    required this.onTabSwitch,
    required this.currentTabType,
  });

  List<Widget> _buildBottomNavigationBarItems() {
    return data.map((model) {
      final isSel = currentTabType == model.type;
      return Expanded(
        child: TabBarItem(
          model: model,
          isSel: isSel,
          onTap: () => onTabSwitch.call(model.type),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final double paddingBottom = MediaQuery.of(context).padding.bottom;
    return Container(
      height: 49.gw + paddingBottom,
      padding: EdgeInsets.only(bottom: paddingBottom),
      color: context.theme.bottomNavigationBarTheme.backgroundColor,
      alignment: Alignment.center,
      child: Row(children: _buildBottomNavigationBarItems()),
    );
  }
}

class TabBarItem extends StatelessWidget {
  final BottomNavConfig model;
  final bool isSel;
  final GestureTapCallback? onTap;

  const TabBarItem({
    super.key,
    required this.model,
    required this.isSel,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildIcon(context),
          const SizedBox(height: 1),
          Text(
            model.title.tr(),
            style: TextStyle(
                color: isSel
                    ? context.theme.bottomNavigationBarTheme.selectedItemColor
                    : context.theme.bottomNavigationBarTheme.unselectedItemColor,
                fontSize: 12.gw),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(BuildContext context) {
    final size = Size(20.gw, 16.gw);
    if (!isSel) {
      return SizedBox(
        width: size.width,
        height: size.height,
        child: IconHelper.loadAsset(
          model.icon,
          color: context.theme.bottomNavigationBarTheme.unselectedItemColor,
        ),
      );
    }

    final isTemplateD = AppConfig.instance.skinStyle == AppSkinStyle.kTemplateD;
    return SizedBox(
      width: size.width,
      height: size.height,
      child: IconHelper.loadAsset(
        model.icon,
        color: context.theme.bottomNavigationBarTheme.selectedItemColor,
        shouldEnableThemeGradient: !isTemplateD,
      ),
    );
  }
}
