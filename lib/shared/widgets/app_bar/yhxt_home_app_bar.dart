import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications/notifications_cubit.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';

import '../../../core/dependency_injection/injectable.dart';
import '../../../features/home/<USER>/settings_menu.dart';
import '../../../features/market/logic/search/search_cubit.dart';
import '../../../features/market/market_search_screen.dart';
import '../../constants/assets.dart';

class YhxtHomeAppBar extends StatelessWidget {
  const YhxtHomeAppBar({super.key});

  void _showSettingsMenu(BuildContext context) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);
    final RelativeRect position = RelativeRect.fromSize(
      Rect.fromLTRB(buttonPosition.dx + button.size.width - 200.gw, buttonPosition.dy + 50.gh,
          buttonPosition.dx + button.size.width, buttonPosition.dy + 100.gh),
      Size(110, 160),
    );

    showMenu(
      context: context,
      position: position,
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 200.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: SettingsMenu(),
        ),
      ],
    );
  }

  @override
  AppBar build(BuildContext context) {
    final sysSettings = context.read<SysSettingsCubit>().state.maybeWhen(
          loaded: (sysSettings, _) => sysSettings,
          orElse: () => null,
        );
    final imageUrl = (isDarkMode(context)
        ? (sysSettings?.logoDark ?? Assets.appLogoTitleDark)
        : (sysSettings?.logoLight ?? Assets.appLogoTitle));

    return AppBar(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Row(
        children: [
          getLogoWidget(),
          const Spacer(),
          InkWell(
            onTap: () => _showSettingsMenu(context),
            child: BlocSelector<NotificationsCubit, NotificationsState, int?>(
              selector: (state) => state.notificationCount,
              builder: (context, notificationCount) {
                return Badge(
                    offset: const Offset(10, -10),
                    isLabelVisible: notificationCount != null && notificationCount > 0,
                    child: Image.asset(
                      "assets/images/icon_setting.png",
                      width: 26.gw,
                      height: 26.gw,
                    ));
              },
            ),
          ),
          SizedBox(width: 5.gw),
          InkWell(
            onTap: () => AuthUtils.verifyAuth(
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BlocProvider(
                    create: (context) => getIt<SearchCubit>(),
                    child: const MarketSearchScreen(),
                  ),
                ),
              ),
            ),
            child: Container(
                width: 40.gw,
                height: 40.gw,
                alignment: Alignment.center,
                child: Image.asset(
                  "assets/images/icon_search.png",
                  width: 26.gw,
                  height: 26.gw,
                )),
          ),
        ],
      ),
    );
  }

  Widget getLogoWidget() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
            borderRadius: BorderRadius.circular(7), // 圆角半径
            child: Image.asset(
              "assets/images/logo/app_logo.png",
              width: 38.gw,
              height: 38.gw,
            )),
        SizedBox(width: 11.gw),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "appName".tr(),
              style: TextStyle(color: Colors.white, fontSize: 18.gsp, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 2.gw),
            Text(
              "上海沅和股权投资基金管理有限公司",
              style: TextStyle(color: Colors.white, fontSize: 10.gsp),
            ),
          ],
        )
      ],
    );
  }

  Color? getIconColor(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateC => null,
      AppSkinStyle.kTemplateD => null,
      _ => context.colorTheme.textRegular,
    };
  }
}
